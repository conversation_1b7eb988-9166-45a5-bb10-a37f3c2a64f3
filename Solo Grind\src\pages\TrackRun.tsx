
import { useState, useEffect, useRef } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { GlassCard } from "@/components/GlassCard";
import { BottomNav } from "@/components/BottomNav";
import { toast } from "sonner";
import { Loader2, Play, Square, Timer, Footprints, Target, TrendingUp, Calendar, MapPin } from "lucide-react";
import { getDistanceFromLatLonInKm } from "@/lib/geo";
import { useTracking, useTimeTracking } from "@/hooks/useTracking";
import { logUserAction, logError } from "@/lib/logger";
import { useQuery } from "@tanstack/react-query";

type GeolocationCoords = {
  latitude: number;
  longitude: number;
};

export default function TrackRun() {
  const { user } = useAuth();
  const { trackWorkoutCompletion, trackButtonClick, trackError } = useTracking();
  const [isTracking, setIsTracking] = useState(false);
  const [distance, setDistance] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Track time spent on track run page
  useTimeTracking('Track Run');

  const watchId = useRef<number | null>(null);
  const lastPosition = useRef<GeolocationCoords | null>(null);
  const timerInterval = useRef<ReturnType<typeof setInterval> | null>(null);

  // Fetch user settings for daily goal
  const { data: userSettings } = useQuery({
    queryKey: ['user-settings', user?.id],
    queryFn: async () => {
      if (!user) return null;
      const { data, error } = await supabase
        .from('user_settings')
        .select('daily_goal_km')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching user settings:', error);
        return null;
      }
      return data;
    },
    enabled: !!user,
  });

  // Fetch recent activities (last 5)
  const { data: recentActivities } = useQuery({
    queryKey: ['recent-activities', user?.id],
    queryFn: async () => {
      if (!user) return null;
      const { data, error } = await supabase
        .from('user_activities')
        .select('distance_km, activity_date, activity_type')
        .eq('user_id', user.id)
        .order('activity_date', { ascending: false })
        .limit(5);

      if (error) {
        console.error('Error fetching recent activities:', error);
        return null;
      }
      return data;
    },
    enabled: !!user,
  });

  // Fetch today's progress
  const { data: todayActivities } = useQuery({
    queryKey: ['today-activities', user?.id],
    queryFn: async () => {
      if (!user) return null;
      const today = new Date().toISOString().split('T')[0];

      const { data, error } = await supabase
        .from('user_activities')
        .select('distance_km')
        .eq('user_id', user.id)
        .eq('activity_date', today);

      if (error) {
        console.error('Error fetching today activities:', error);
        return null;
      }
      return data;
    },
    enabled: !!user,
  });

  const dailyGoal = userSettings?.daily_goal_km || 6;
  const todayDistance = todayActivities?.reduce((sum, activity) => sum + Number(activity.distance_km), 0) || 0;
  const dailyProgress = dailyGoal > 0 ? Math.min((todayDistance / dailyGoal) * 100, 100) : 0;

  useEffect(() => {
    // Clear watch and interval on component unmount
    return () => {
      if (watchId.current) navigator.geolocation.clearWatch(watchId.current);
      if (timerInterval.current) clearInterval(timerInterval.current);
    };
  }, []);

  const handleStart = () => {
    if (!navigator.geolocation) {
      const errorMsg = "Geolocation is not supported by your browser.";
      setError(errorMsg);
      trackError(errorMsg, { feature: 'geolocation' });
      logError('Geolocation not supported', { userAgent: navigator.userAgent });
      return;
    }

    trackButtonClick('start_workout', { workout_type: 'run' });
    logUserAction('Started workout tracking', { type: 'run' });

    // Reset state for a new run
    setDistance(0);
    setDuration(0);
    setError(null);
    lastPosition.current = null;
    setIsTracking(true);

    timerInterval.current = setInterval(() => {
      setDuration(d => d + 1);
    }, 1000);

    watchId.current = navigator.geolocation.watchPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        if (lastPosition.current) {
          const newDistance = getDistanceFromLatLonInKm(
            lastPosition.current.latitude,
            lastPosition.current.longitude,
            latitude,
            longitude
          );
          setDistance((d) => d + newDistance);
        }
        lastPosition.current = { latitude, longitude };
      },
      (err) => {
        setError(`Error getting location: ${err.message}`);
        handleStop(false); // Stop tracking on error
      },
      { enableHighAccuracy: true, timeout: 10000, maximumAge: 0 }
    );
  };

  const handleStop = async (shouldSave = true) => {
    if (watchId.current) navigator.geolocation.clearWatch(watchId.current);
    if (timerInterval.current) clearInterval(timerInterval.current);
    
    setIsTracking(false);

    if (shouldSave && distance > 0 && user) {
      setIsSaving(true);

      const workoutData = {
        user_id: user.id,
        distance_km: parseFloat(distance.toFixed(2)),
        activity_type: "Run",
        activity_date: new Date().toISOString().split("T")[0],
      };

      const { error: insertError } = await supabase.from("user_activities").insert(workoutData);
      setIsSaving(false);

      if (insertError) {
        const errorMsg = "Failed to save run.";
        toast.error(errorMsg, { description: insertError.message });
        trackError(errorMsg, { error: insertError.message, distance, duration });
        logError('Failed to save workout', { error: insertError, workoutData });
      } else {
        const successMsg = `You ran ${distance.toFixed(2)} km.`;
        toast.success("Run saved successfully!", { description: successMsg });

        // Track the completed workout
        trackWorkoutCompletion({
          type: 'Run',
          distance: parseFloat(distance.toFixed(2)),
          duration: duration,
        });

        logUserAction('Workout saved successfully', {
          distance: distance.toFixed(2),
          duration,
          type: 'Run'
        });

        // Reset for next run
        setDistance(0);
        setDuration(0);
      }
    } else if (shouldSave) {
      trackButtonClick('stop_workout_no_save', {
        reason: distance <= 0 ? 'no_distance' : 'no_user',
        distance,
        duration
      });
    }
  };
  
  const formatDuration = (seconds: number) => {
    const h = Math.floor(seconds / 3600).toString().padStart(2, '0');
    const m = Math.floor((seconds % 3600) / 60).toString().padStart(2, '0');
    const s = (seconds % 60).toString().padStart(2, '0');
    return `${h}:${m}:${s}`;
  };

  return (
    <>
      <div
        style={{ backgroundImage: "url('https://images.unsplash.com/photo-1470813740244-df37b8c1edcb')" }}
        className="fixed inset-0 bg-center bg-cover filter blur-sm scale-105"
      />
      <div className="relative z-10 pb-20 pt-6 min-h-screen bg-black/70">
        <div className="px-4">
          <h1 className="text-4xl font-bold gradient-title mb-2 text-center">Track Your Run</h1>

          {/* Today's Progress */}
          <GlassCard className="mb-6 p-4 max-w-md mx-auto">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <Target className="text-electric" size={20} />
                <span className="text-white/80 font-semibold">Today's Progress</span>
              </div>
              <span className="text-xs text-electric font-semibold">
                {todayDistance.toFixed(1)} / {dailyGoal} km
              </span>
            </div>
            <div className="w-full bg-white/10 rounded-full h-2">
              <div
                className="bg-gradient-to-r from-electric to-purple h-2 rounded-full transition-all duration-300"
                style={{ width: `${dailyProgress}%` }}
              />
            </div>
            <div className="flex justify-between text-xs text-white/60 mt-1">
              <span>{dailyProgress.toFixed(0)}% complete</span>
              <span>{(dailyGoal - todayDistance).toFixed(1)} km to go</span>
            </div>
          </GlassCard>

          {/* Current Session Metrics */}
          <div className="flex flex-col items-center justify-center mb-6">
            <div className="w-full max-w-sm space-y-4">
              <GlassCard className="p-6 flex flex-col items-center">
                <Footprints className="text-electric mb-2" size={32} />
                <span className="text-sm text-white/70">DISTANCE</span>
                <span className="text-4xl font-bold text-white">{distance.toFixed(2)} km</span>
                {isTracking && (
                  <div className="flex items-center gap-1 mt-2">
                    <div className="w-2 h-2 bg-electric rounded-full animate-pulse" />
                    <span className="text-xs text-electric">Recording...</span>
                  </div>
                )}
              </GlassCard>

              <GlassCard className="p-6 flex flex-col items-center">
                <Timer className="text-electric mb-2" size={32} />
                <span className="text-sm text-white/70">DURATION</span>
                <span className="text-4xl font-bold text-white">{formatDuration(duration)}</span>
                {distance > 0 && (
                  <span className="text-xs text-white/60 mt-1">
                    Avg: {duration > 0 ? (duration / 60 / distance).toFixed(1) : '0'} min/km
                  </span>
                )}
              </GlassCard>
            </div>
          </div>
        </div>

        {/* Action Button */}
        <div className="flex flex-col items-center mb-6">
          {isTracking ? (
            <Button onClick={() => handleStop()} size="lg" className="bg-red-600 hover:bg-red-700 text-white font-bold py-4 px-10 rounded-full text-xl shadow-glow">
              <Square className="mr-2" size={24} /> Stop Run
            </Button>
          ) : (
            <Button onClick={handleStart} size="lg" className="bg-electric hover:bg-purple text-white font-bold py-4 px-10 rounded-full text-xl shadow-glow" disabled={isSaving}>
              {isSaving ? (
                <>
                  <Loader2 className="mr-2 h-6 w-6 animate-spin" /> Saving...
                </>
              ) : (
                <>
                  <Play className="mr-2" size={24} /> Start Run
                </>
              )}
            </Button>
          )}

          {error && (
            <div className="mt-4 p-3 bg-red-500/20 border border-red-500/50 rounded-lg max-w-sm">
              <p className="text-red-300 text-sm text-center">{error}</p>
            </div>
          )}
        </div>

        {/* Recent Activities */}
        {!isTracking && recentActivities && recentActivities.length > 0 && (
          <div className="px-4 mb-6">
            <GlassCard className="p-4 max-w-md mx-auto">
              <div className="flex items-center gap-2 mb-3">
                <TrendingUp className="text-electric" size={20} />
                <span className="text-white/80 font-semibold">Recent Activities</span>
              </div>
              <div className="space-y-2">
                {recentActivities.slice(0, 3).map((activity, index) => (
                  <div key={index} className="flex items-center justify-between py-2 border-b border-white/10 last:border-b-0">
                    <div className="flex items-center gap-2">
                      <Calendar className="text-white/60" size={14} />
                      <span className="text-white/70 text-sm">
                        {new Date(activity.activity_date).toLocaleDateString('en-US', {
                          month: 'short',
                          day: 'numeric'
                        })}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-white text-sm font-semibold">
                        {Number(activity.distance_km).toFixed(1)} km
                      </span>
                      <span className="text-white/60 text-xs">
                        {activity.activity_type}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </GlassCard>
          </div>
        )}

        <BottomNav />
      </div>
    </>
  );
}
