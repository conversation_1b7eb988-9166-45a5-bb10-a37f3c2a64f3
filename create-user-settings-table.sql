-- Manual SQL script to create user_settings table
-- Run this in Supabase SQL Editor

-- Create user_settings table to store user preferences and goals
CREATE TABLE IF NOT EXISTS public.user_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID UNIQUE NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    daily_goal_km NUMERIC(5, 2) NOT NULL DEFAULT 6.00,
    weekly_goal_km NUMERIC(5, 2) NOT NULL DEFAULT 42.00,
    preferred_activity_type TEXT NOT NULL DEFAULT 'Run',
    notifications_enabled BOOLEAN NOT NULL DEFAULT true,
    privacy_level TEXT NOT NULL DEFAULT 'public' CHECK (privacy_level IN ('public', 'friends', 'private')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Add comments for clarity
COMMENT ON TABLE public.user_settings IS 'Stores user preferences and fitness goals.';
COMMENT ON COLUMN public.user_settings.daily_goal_km IS 'Daily distance goal in kilometers.';
COMMENT ON COLUMN public.user_settings.weekly_goal_km IS 'Weekly distance goal in kilometers.';
COMMENT ON COLUMN public.user_settings.preferred_activity_type IS 'User preferred activity type (Run, Walk, Cycle, etc.).';
COMMENT ON COLUMN public.user_settings.notifications_enabled IS 'Whether user wants to receive notifications.';
COMMENT ON COLUMN public.user_settings.privacy_level IS 'User privacy level for sharing activities.';

-- Enable Row-Level Security
ALTER TABLE public.user_settings ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view their own settings" 
ON public.user_settings FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own settings" 
ON public.user_settings FOR INSERT 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own settings" 
ON public.user_settings FOR UPDATE 
USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all settings" 
ON public.user_settings FOR SELECT 
TO authenticated
USING (
  EXISTS (
    SELECT 1
    FROM public.user_roles
    WHERE user_roles.user_id = auth.uid() AND user_roles.role = 'admin'
  )
);

-- Function to automatically create default settings for new users
CREATE OR REPLACE FUNCTION public.create_default_user_settings()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Create default settings for the new user
  INSERT INTO public.user_settings (user_id, daily_goal_km, weekly_goal_km)
  VALUES (NEW.id, 6.00, 42.00)
  ON CONFLICT (user_id) DO NOTHING;
  
  RETURN NEW;
END;
$$;

-- Trigger to create default settings when a new profile is created
DROP TRIGGER IF EXISTS on_profile_created_create_settings ON public.profiles;
CREATE TRIGGER on_profile_created_create_settings
AFTER INSERT ON public.profiles
FOR EACH ROW
EXECUTE FUNCTION public.create_default_user_settings();

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_user_settings_updated_at()
RETURNS trigger
LANGUAGE plpgsql
AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$;

-- Trigger to update updated_at on settings changes
DROP TRIGGER IF EXISTS on_user_settings_updated ON public.user_settings;
CREATE TRIGGER on_user_settings_updated
BEFORE UPDATE ON public.user_settings
FOR EACH ROW
EXECUTE FUNCTION public.update_user_settings_updated_at();

-- Create default settings for existing users
INSERT INTO public.user_settings (user_id, daily_goal_km, weekly_goal_km)
SELECT id, 6.00, 42.00 
FROM public.profiles 
WHERE id NOT IN (SELECT user_id FROM public.user_settings WHERE user_id IS NOT NULL)
ON CONFLICT (user_id) DO NOTHING;
