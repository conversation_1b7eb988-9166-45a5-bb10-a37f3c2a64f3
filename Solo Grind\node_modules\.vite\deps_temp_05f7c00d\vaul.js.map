{"version": 3, "sources": ["../../vaul/dist/index.mjs"], "sourcesContent": ["'use client';\nfunction __insertCSS(code) {\n  if (!code || typeof document == 'undefined') return\n  let head = document.head || document.getElementsByTagName('head')[0]\n  let style = document.createElement('style')\n  style.type = 'text/css'\n  head.appendChild(style)\n  ;style.styleSheet ? (style.styleSheet.cssText = code) : style.appendChild(document.createTextNode(code))\n}\n\nimport * as DialogPrimitive from '@radix-ui/react-dialog';\nimport * as React from 'react';\nimport React__default, { useLayoutEffect, useEffect, useMemo } from 'react';\n\nconst DrawerContext = React__default.createContext({\n    drawerRef: {\n        current: null\n    },\n    overlayRef: {\n        current: null\n    },\n    onPress: ()=>{},\n    onRelease: ()=>{},\n    onDrag: ()=>{},\n    onNestedDrag: ()=>{},\n    onNestedOpenChange: ()=>{},\n    onNestedRelease: ()=>{},\n    openProp: undefined,\n    dismissible: false,\n    isOpen: false,\n    isDragging: false,\n    keyboardIsOpen: {\n        current: false\n    },\n    snapPointsOffset: null,\n    snapPoints: null,\n    handleOnly: false,\n    modal: false,\n    shouldFade: false,\n    activeSnapPoint: null,\n    onOpenChange: ()=>{},\n    setActiveSnapPoint: ()=>{},\n    closeDrawer: ()=>{},\n    direction: 'bottom',\n    shouldScaleBackground: false,\n    setBackgroundColorOnScale: true,\n    noBodyStyles: false,\n    container: null,\n    autoFocus: false\n});\nconst useDrawerContext = ()=>{\n    const context = React__default.useContext(DrawerContext);\n    if (!context) {\n        throw new Error('useDrawerContext must be used within a Drawer.Root');\n    }\n    return context;\n};\n\n__insertCSS(\"[data-vaul-drawer]{touch-action:none;will-change:transform;transition:transform .5s cubic-bezier(.32, .72, 0, 1);animation-duration:.5s;animation-timing-function:cubic-bezier(0.32,0.72,0,1)}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=bottom][data-state=open]{animation-name:slideFromBottom}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=bottom][data-state=closed]{animation-name:slideToBottom}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=top][data-state=open]{animation-name:slideFromTop}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=top][data-state=closed]{animation-name:slideToTop}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=left][data-state=open]{animation-name:slideFromLeft}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=left][data-state=closed]{animation-name:slideToLeft}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=right][data-state=open]{animation-name:slideFromRight}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=right][data-state=closed]{animation-name:slideToRight}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=bottom]{transform:translate3d(0,100%,0)}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=top]{transform:translate3d(0,-100%,0)}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=left]{transform:translate3d(-100%,0,0)}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=right]{transform:translate3d(100%,0,0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=top]{transform:translate3d(0,var(--snap-point-height,0),0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=bottom]{transform:translate3d(0,var(--snap-point-height,0),0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=left]{transform:translate3d(var(--snap-point-height,0),0,0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=right]{transform:translate3d(var(--snap-point-height,0),0,0)}[data-vaul-overlay][data-vaul-snap-points=false]{animation-duration:.5s;animation-timing-function:cubic-bezier(0.32,0.72,0,1)}[data-vaul-overlay][data-vaul-snap-points=false][data-state=open]{animation-name:fadeIn}[data-vaul-overlay][data-state=closed]{animation-name:fadeOut}[data-vaul-overlay][data-vaul-snap-points=true]{opacity:0;transition:opacity .5s cubic-bezier(.32, .72, 0, 1)}[data-vaul-overlay][data-vaul-snap-points=true]{opacity:1}[data-vaul-drawer]:not([data-vaul-custom-container=true])::after{content:'';position:absolute;background:inherit;background-color:inherit}[data-vaul-drawer][data-vaul-drawer-direction=top]::after{top:initial;bottom:100%;left:0;right:0;height:200%}[data-vaul-drawer][data-vaul-drawer-direction=bottom]::after{top:100%;bottom:initial;left:0;right:0;height:200%}[data-vaul-drawer][data-vaul-drawer-direction=left]::after{left:initial;right:100%;top:0;bottom:0;width:200%}[data-vaul-drawer][data-vaul-drawer-direction=right]::after{left:100%;right:initial;top:0;bottom:0;width:200%}[data-vaul-overlay][data-vaul-snap-points=true]:not([data-vaul-snap-points-overlay=true]):not(\\n[data-state=closed]\\n){opacity:0}[data-vaul-overlay][data-vaul-snap-points-overlay=true]{opacity:1}[data-vaul-handle]{display:block;position:relative;opacity:.7;background:#e2e2e4;margin-left:auto;margin-right:auto;height:5px;width:32px;border-radius:1rem;touch-action:pan-y}[data-vaul-handle]:active,[data-vaul-handle]:hover{opacity:1}[data-vaul-handle-hitarea]{position:absolute;left:50%;top:50%;transform:translate(-50%,-50%);width:max(100%,2.75rem);height:max(100%,2.75rem);touch-action:inherit}@media (hover:hover) and (pointer:fine){[data-vaul-drawer]{user-select:none}}@media (pointer:fine){[data-vaul-handle-hitarea]:{width:100%;height:100%}}@keyframes fadeIn{from{opacity:0}to{opacity:1}}@keyframes fadeOut{to{opacity:0}}@keyframes slideFromBottom{from{transform:translate3d(0,100%,0)}to{transform:translate3d(0,0,0)}}@keyframes slideToBottom{to{transform:translate3d(0,100%,0)}}@keyframes slideFromTop{from{transform:translate3d(0,-100%,0)}to{transform:translate3d(0,0,0)}}@keyframes slideToTop{to{transform:translate3d(0,-100%,0)}}@keyframes slideFromLeft{from{transform:translate3d(-100%,0,0)}to{transform:translate3d(0,0,0)}}@keyframes slideToLeft{to{transform:translate3d(-100%,0,0)}}@keyframes slideFromRight{from{transform:translate3d(100%,0,0)}to{transform:translate3d(0,0,0)}}@keyframes slideToRight{to{transform:translate3d(100%,0,0)}}\");\n\n// This code comes from https://github.com/adobe/react-spectrum/blob/main/packages/%40react-aria/overlays/src/usePreventScroll.ts\nconst KEYBOARD_BUFFER = 24;\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' ? useLayoutEffect : useEffect;\nfunction chain$1(...callbacks) {\n    return (...args)=>{\n        for (let callback of callbacks){\n            if (typeof callback === 'function') {\n                callback(...args);\n            }\n        }\n    };\n}\nfunction isMac() {\n    return testPlatform(/^Mac/);\n}\nfunction isIPhone() {\n    return testPlatform(/^iPhone/);\n}\nfunction isSafari() {\n    return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);\n}\nfunction isIPad() {\n    return testPlatform(/^iPad/) || // iPadOS 13 lies and says it's a Mac, but we can distinguish by detecting touch support.\n    isMac() && navigator.maxTouchPoints > 1;\n}\nfunction isIOS() {\n    return isIPhone() || isIPad();\n}\nfunction testPlatform(re) {\n    return typeof window !== 'undefined' && window.navigator != null ? re.test(window.navigator.platform) : undefined;\n}\n// @ts-ignore\nconst visualViewport = typeof document !== 'undefined' && window.visualViewport;\nfunction isScrollable(node) {\n    let style = window.getComputedStyle(node);\n    return /(auto|scroll)/.test(style.overflow + style.overflowX + style.overflowY);\n}\nfunction getScrollParent(node) {\n    if (isScrollable(node)) {\n        node = node.parentElement;\n    }\n    while(node && !isScrollable(node)){\n        node = node.parentElement;\n    }\n    return node || document.scrollingElement || document.documentElement;\n}\n// HTML input types that do not cause the software keyboard to appear.\nconst nonTextInputTypes = new Set([\n    'checkbox',\n    'radio',\n    'range',\n    'color',\n    'file',\n    'image',\n    'button',\n    'submit',\n    'reset'\n]);\n// The number of active usePreventScroll calls. Used to determine whether to revert back to the original page style/scroll position\nlet preventScrollCount = 0;\nlet restore;\n/**\n * Prevents scrolling on the document body on mount, and\n * restores it on unmount. Also ensures that content does not\n * shift due to the scrollbars disappearing.\n */ function usePreventScroll(options = {}) {\n    let { isDisabled } = options;\n    useIsomorphicLayoutEffect(()=>{\n        if (isDisabled) {\n            return;\n        }\n        preventScrollCount++;\n        if (preventScrollCount === 1) {\n            if (isIOS()) {\n                restore = preventScrollMobileSafari();\n            }\n        }\n        return ()=>{\n            preventScrollCount--;\n            if (preventScrollCount === 0) {\n                restore == null ? void 0 : restore();\n            }\n        };\n    }, [\n        isDisabled\n    ]);\n}\n// Mobile Safari is a whole different beast. Even with overflow: hidden,\n// it still scrolls the page in many situations:\n//\n// 1. When the bottom toolbar and address bar are collapsed, page scrolling is always allowed.\n// 2. When the keyboard is visible, the viewport does not resize. Instead, the keyboard covers part of\n//    it, so it becomes scrollable.\n// 3. When tapping on an input, the page always scrolls so that the input is centered in the visual viewport.\n//    This may cause even fixed position elements to scroll off the screen.\n// 4. When using the next/previous buttons in the keyboard to navigate between inputs, the whole page always\n//    scrolls, even if the input is inside a nested scrollable element that could be scrolled instead.\n//\n// In order to work around these cases, and prevent scrolling without jankiness, we do a few things:\n//\n// 1. Prevent default on `touchmove` events that are not in a scrollable element. This prevents touch scrolling\n//    on the window.\n// 2. Prevent default on `touchmove` events inside a scrollable element when the scroll position is at the\n//    top or bottom. This avoids the whole page scrolling instead, but does prevent overscrolling.\n// 3. Prevent default on `touchend` events on input elements and handle focusing the element ourselves.\n// 4. When focusing an input, apply a transform to trick Safari into thinking the input is at the top\n//    of the page, which prevents it from scrolling the page. After the input is focused, scroll the element\n//    into view ourselves, without scrolling the whole page.\n// 5. Offset the body by the scroll position using a negative margin and scroll to the top. This should appear the\n//    same visually, but makes the actual scroll position always zero. This is required to make all of the\n//    above work or Safari will still try to scroll the page when focusing an input.\n// 6. As a last resort, handle window scroll events, and scroll back to the top. This can happen when attempting\n//    to navigate to an input with the next/previous buttons that's outside a modal.\nfunction preventScrollMobileSafari() {\n    let scrollable;\n    let lastY = 0;\n    let onTouchStart = (e)=>{\n        // Store the nearest scrollable parent element from the element that the user touched.\n        scrollable = getScrollParent(e.target);\n        if (scrollable === document.documentElement && scrollable === document.body) {\n            return;\n        }\n        lastY = e.changedTouches[0].pageY;\n    };\n    let onTouchMove = (e)=>{\n        // Prevent scrolling the window.\n        if (!scrollable || scrollable === document.documentElement || scrollable === document.body) {\n            e.preventDefault();\n            return;\n        }\n        // Prevent scrolling up when at the top and scrolling down when at the bottom\n        // of a nested scrollable area, otherwise mobile Safari will start scrolling\n        // the window instead. Unfortunately, this disables bounce scrolling when at\n        // the top but it's the best we can do.\n        let y = e.changedTouches[0].pageY;\n        let scrollTop = scrollable.scrollTop;\n        let bottom = scrollable.scrollHeight - scrollable.clientHeight;\n        if (bottom === 0) {\n            return;\n        }\n        if (scrollTop <= 0 && y > lastY || scrollTop >= bottom && y < lastY) {\n            e.preventDefault();\n        }\n        lastY = y;\n    };\n    let onTouchEnd = (e)=>{\n        let target = e.target;\n        // Apply this change if we're not already focused on the target element\n        if (isInput(target) && target !== document.activeElement) {\n            e.preventDefault();\n            // Apply a transform to trick Safari into thinking the input is at the top of the page\n            // so it doesn't try to scroll it into view. When tapping on an input, this needs to\n            // be done before the \"focus\" event, so we have to focus the element ourselves.\n            target.style.transform = 'translateY(-2000px)';\n            target.focus();\n            requestAnimationFrame(()=>{\n                target.style.transform = '';\n            });\n        }\n    };\n    let onFocus = (e)=>{\n        let target = e.target;\n        if (isInput(target)) {\n            // Transform also needs to be applied in the focus event in cases where focus moves\n            // other than tapping on an input directly, e.g. the next/previous buttons in the\n            // software keyboard. In these cases, it seems applying the transform in the focus event\n            // is good enough, whereas when tapping an input, it must be done before the focus event. 🤷‍♂️\n            target.style.transform = 'translateY(-2000px)';\n            requestAnimationFrame(()=>{\n                target.style.transform = '';\n                // This will have prevented the browser from scrolling the focused element into view,\n                // so we need to do this ourselves in a way that doesn't cause the whole page to scroll.\n                if (visualViewport) {\n                    if (visualViewport.height < window.innerHeight) {\n                        // If the keyboard is already visible, do this after one additional frame\n                        // to wait for the transform to be removed.\n                        requestAnimationFrame(()=>{\n                            scrollIntoView(target);\n                        });\n                    } else {\n                        // Otherwise, wait for the visual viewport to resize before scrolling so we can\n                        // measure the correct position to scroll to.\n                        visualViewport.addEventListener('resize', ()=>scrollIntoView(target), {\n                            once: true\n                        });\n                    }\n                }\n            });\n        }\n    };\n    let onWindowScroll = ()=>{\n        // Last resort. If the window scrolled, scroll it back to the top.\n        // It should always be at the top because the body will have a negative margin (see below).\n        window.scrollTo(0, 0);\n    };\n    // Record the original scroll position so we can restore it.\n    // Then apply a negative margin to the body to offset it by the scroll position. This will\n    // enable us to scroll the window to the top, which is required for the rest of this to work.\n    let scrollX = window.pageXOffset;\n    let scrollY = window.pageYOffset;\n    let restoreStyles = chain$1(setStyle(document.documentElement, 'paddingRight', `${window.innerWidth - document.documentElement.clientWidth}px`));\n    // Scroll to the top. The negative margin on the body will make this appear the same.\n    window.scrollTo(0, 0);\n    let removeEvents = chain$1(addEvent(document, 'touchstart', onTouchStart, {\n        passive: false,\n        capture: true\n    }), addEvent(document, 'touchmove', onTouchMove, {\n        passive: false,\n        capture: true\n    }), addEvent(document, 'touchend', onTouchEnd, {\n        passive: false,\n        capture: true\n    }), addEvent(document, 'focus', onFocus, true), addEvent(window, 'scroll', onWindowScroll));\n    return ()=>{\n        // Restore styles and scroll the page back to where it was.\n        restoreStyles();\n        removeEvents();\n        window.scrollTo(scrollX, scrollY);\n    };\n}\n// Sets a CSS property on an element, and returns a function to revert it to the previous value.\nfunction setStyle(element, style, value) {\n    let cur = element.style[style];\n    element.style[style] = value;\n    return ()=>{\n        element.style[style] = cur;\n    };\n}\n// Adds an event listener to an element, and returns a function to remove it.\nfunction addEvent(target, event, handler, options) {\n    // @ts-ignore\n    target.addEventListener(event, handler, options);\n    return ()=>{\n        // @ts-ignore\n        target.removeEventListener(event, handler, options);\n    };\n}\nfunction scrollIntoView(target) {\n    let root = document.scrollingElement || document.documentElement;\n    while(target && target !== root){\n        // Find the parent scrollable element and adjust the scroll position if the target is not already in view.\n        let scrollable = getScrollParent(target);\n        if (scrollable !== document.documentElement && scrollable !== document.body && scrollable !== target) {\n            let scrollableTop = scrollable.getBoundingClientRect().top;\n            let targetTop = target.getBoundingClientRect().top;\n            let targetBottom = target.getBoundingClientRect().bottom;\n            // Buffer is needed for some edge cases\n            const keyboardHeight = scrollable.getBoundingClientRect().bottom + KEYBOARD_BUFFER;\n            if (targetBottom > keyboardHeight) {\n                scrollable.scrollTop += targetTop - scrollableTop;\n            }\n        }\n        // @ts-ignore\n        target = scrollable.parentElement;\n    }\n}\nfunction isInput(target) {\n    return target instanceof HTMLInputElement && !nonTextInputTypes.has(target.type) || target instanceof HTMLTextAreaElement || target instanceof HTMLElement && target.isContentEditable;\n}\n\n// This code comes from https://github.com/radix-ui/primitives/tree/main/packages/react/compose-refs\n/**\n * Set a given ref to a given value\n * This utility takes care of different types of refs: callback refs and RefObject(s)\n */ function setRef(ref, value) {\n    if (typeof ref === 'function') {\n        ref(value);\n    } else if (ref !== null && ref !== undefined) {\n        ref.current = value;\n    }\n}\n/**\n * A utility to compose multiple refs together\n * Accepts callback refs and RefObject(s)\n */ function composeRefs(...refs) {\n    return (node)=>refs.forEach((ref)=>setRef(ref, node));\n}\n/**\n * A custom hook that composes multiple refs\n * Accepts callback refs and RefObject(s)\n */ function useComposedRefs(...refs) {\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    return React.useCallback(composeRefs(...refs), refs);\n}\n\nconst cache = new WeakMap();\nfunction set(el, styles, ignoreCache = false) {\n    if (!el || !(el instanceof HTMLElement)) return;\n    let originalStyles = {};\n    Object.entries(styles).forEach(([key, value])=>{\n        if (key.startsWith('--')) {\n            el.style.setProperty(key, value);\n            return;\n        }\n        originalStyles[key] = el.style[key];\n        el.style[key] = value;\n    });\n    if (ignoreCache) return;\n    cache.set(el, originalStyles);\n}\nfunction reset(el, prop) {\n    if (!el || !(el instanceof HTMLElement)) return;\n    let originalStyles = cache.get(el);\n    if (!originalStyles) {\n        return;\n    }\n    {\n        el.style[prop] = originalStyles[prop];\n    }\n}\nconst isVertical = (direction)=>{\n    switch(direction){\n        case 'top':\n        case 'bottom':\n            return true;\n        case 'left':\n        case 'right':\n            return false;\n        default:\n            return direction;\n    }\n};\nfunction getTranslate(element, direction) {\n    if (!element) {\n        return null;\n    }\n    const style = window.getComputedStyle(element);\n    const transform = // @ts-ignore\n    style.transform || style.webkitTransform || style.mozTransform;\n    let mat = transform.match(/^matrix3d\\((.+)\\)$/);\n    if (mat) {\n        // https://developer.mozilla.org/en-US/docs/Web/CSS/transform-function/matrix3d\n        return parseFloat(mat[1].split(', ')[isVertical(direction) ? 13 : 12]);\n    }\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/transform-function/matrix\n    mat = transform.match(/^matrix\\((.+)\\)$/);\n    return mat ? parseFloat(mat[1].split(', ')[isVertical(direction) ? 5 : 4]) : null;\n}\nfunction dampenValue(v) {\n    return 8 * (Math.log(v + 1) - 2);\n}\nfunction assignStyle(element, style) {\n    if (!element) return ()=>{};\n    const prevStyle = element.style.cssText;\n    Object.assign(element.style, style);\n    return ()=>{\n        element.style.cssText = prevStyle;\n    };\n}\n/**\n * Receives functions as arguments and returns a new function that calls all.\n */ function chain(...fns) {\n    return (...args)=>{\n        for (const fn of fns){\n            if (typeof fn === 'function') {\n                // @ts-ignore\n                fn(...args);\n            }\n        }\n    };\n}\n\nconst TRANSITIONS = {\n    DURATION: 0.5,\n    EASE: [\n        0.32,\n        0.72,\n        0,\n        1\n    ]\n};\nconst VELOCITY_THRESHOLD = 0.4;\nconst CLOSE_THRESHOLD = 0.25;\nconst SCROLL_LOCK_TIMEOUT = 100;\nconst BORDER_RADIUS = 8;\nconst NESTED_DISPLACEMENT = 16;\nconst WINDOW_TOP_OFFSET = 26;\nconst DRAG_CLASS = 'vaul-dragging';\n\n// This code comes from https://github.com/radix-ui/primitives/blob/main/packages/react/use-controllable-state/src/useControllableState.tsx\nfunction useCallbackRef(callback) {\n    const callbackRef = React__default.useRef(callback);\n    React__default.useEffect(()=>{\n        callbackRef.current = callback;\n    });\n    // https://github.com/facebook/react/issues/19240\n    return React__default.useMemo(()=>(...args)=>callbackRef.current == null ? void 0 : callbackRef.current.call(callbackRef, ...args), []);\n}\nfunction useUncontrolledState({ defaultProp, onChange }) {\n    const uncontrolledState = React__default.useState(defaultProp);\n    const [value] = uncontrolledState;\n    const prevValueRef = React__default.useRef(value);\n    const handleChange = useCallbackRef(onChange);\n    React__default.useEffect(()=>{\n        if (prevValueRef.current !== value) {\n            handleChange(value);\n            prevValueRef.current = value;\n        }\n    }, [\n        value,\n        prevValueRef,\n        handleChange\n    ]);\n    return uncontrolledState;\n}\nfunction useControllableState({ prop, defaultProp, onChange = ()=>{} }) {\n    const [uncontrolledProp, setUncontrolledProp] = useUncontrolledState({\n        defaultProp,\n        onChange\n    });\n    const isControlled = prop !== undefined;\n    const value = isControlled ? prop : uncontrolledProp;\n    const handleChange = useCallbackRef(onChange);\n    const setValue = React__default.useCallback((nextValue)=>{\n        if (isControlled) {\n            const setter = nextValue;\n            const value = typeof nextValue === 'function' ? setter(prop) : nextValue;\n            if (value !== prop) handleChange(value);\n        } else {\n            setUncontrolledProp(nextValue);\n        }\n    }, [\n        isControlled,\n        prop,\n        setUncontrolledProp,\n        handleChange\n    ]);\n    return [\n        value,\n        setValue\n    ];\n}\n\nfunction useSnapPoints({ activeSnapPointProp, setActiveSnapPointProp, snapPoints, drawerRef, overlayRef, fadeFromIndex, onSnapPointChange, direction = 'bottom', container, snapToSequentialPoint }) {\n    const [activeSnapPoint, setActiveSnapPoint] = useControllableState({\n        prop: activeSnapPointProp,\n        defaultProp: snapPoints == null ? void 0 : snapPoints[0],\n        onChange: setActiveSnapPointProp\n    });\n    const [windowDimensions, setWindowDimensions] = React__default.useState(typeof window !== 'undefined' ? {\n        innerWidth: window.innerWidth,\n        innerHeight: window.innerHeight\n    } : undefined);\n    React__default.useEffect(()=>{\n        function onResize() {\n            setWindowDimensions({\n                innerWidth: window.innerWidth,\n                innerHeight: window.innerHeight\n            });\n        }\n        window.addEventListener('resize', onResize);\n        return ()=>window.removeEventListener('resize', onResize);\n    }, []);\n    const isLastSnapPoint = React__default.useMemo(()=>activeSnapPoint === (snapPoints == null ? void 0 : snapPoints[snapPoints.length - 1]) || null, [\n        snapPoints,\n        activeSnapPoint\n    ]);\n    const activeSnapPointIndex = React__default.useMemo(()=>snapPoints == null ? void 0 : snapPoints.findIndex((snapPoint)=>snapPoint === activeSnapPoint), [\n        snapPoints,\n        activeSnapPoint\n    ]);\n    const shouldFade = snapPoints && snapPoints.length > 0 && (fadeFromIndex || fadeFromIndex === 0) && !Number.isNaN(fadeFromIndex) && snapPoints[fadeFromIndex] === activeSnapPoint || !snapPoints;\n    const snapPointsOffset = React__default.useMemo(()=>{\n        const containerSize = container ? {\n            width: container.getBoundingClientRect().width,\n            height: container.getBoundingClientRect().height\n        } : typeof window !== 'undefined' ? {\n            width: window.innerWidth,\n            height: window.innerHeight\n        } : {\n            width: 0,\n            height: 0\n        };\n        var _snapPoints_map;\n        return (_snapPoints_map = snapPoints == null ? void 0 : snapPoints.map((snapPoint)=>{\n            const isPx = typeof snapPoint === 'string';\n            let snapPointAsNumber = 0;\n            if (isPx) {\n                snapPointAsNumber = parseInt(snapPoint, 10);\n            }\n            if (isVertical(direction)) {\n                const height = isPx ? snapPointAsNumber : windowDimensions ? snapPoint * containerSize.height : 0;\n                if (windowDimensions) {\n                    return direction === 'bottom' ? containerSize.height - height : -containerSize.height + height;\n                }\n                return height;\n            }\n            const width = isPx ? snapPointAsNumber : windowDimensions ? snapPoint * containerSize.width : 0;\n            if (windowDimensions) {\n                return direction === 'right' ? containerSize.width - width : -containerSize.width + width;\n            }\n            return width;\n        })) != null ? _snapPoints_map : [];\n    }, [\n        snapPoints,\n        windowDimensions,\n        container\n    ]);\n    const activeSnapPointOffset = React__default.useMemo(()=>activeSnapPointIndex !== null ? snapPointsOffset == null ? void 0 : snapPointsOffset[activeSnapPointIndex] : null, [\n        snapPointsOffset,\n        activeSnapPointIndex\n    ]);\n    const snapToPoint = React__default.useCallback((dimension)=>{\n        var _snapPointsOffset_findIndex;\n        const newSnapPointIndex = (_snapPointsOffset_findIndex = snapPointsOffset == null ? void 0 : snapPointsOffset.findIndex((snapPointDim)=>snapPointDim === dimension)) != null ? _snapPointsOffset_findIndex : null;\n        onSnapPointChange(newSnapPointIndex);\n        set(drawerRef.current, {\n            transition: `transform ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(',')})`,\n            transform: isVertical(direction) ? `translate3d(0, ${dimension}px, 0)` : `translate3d(${dimension}px, 0, 0)`\n        });\n        if (snapPointsOffset && newSnapPointIndex !== snapPointsOffset.length - 1 && newSnapPointIndex !== fadeFromIndex && newSnapPointIndex < fadeFromIndex) {\n            set(overlayRef.current, {\n                transition: `opacity ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(',')})`,\n                opacity: '0'\n            });\n        } else {\n            set(overlayRef.current, {\n                transition: `opacity ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(',')})`,\n                opacity: '1'\n            });\n        }\n        setActiveSnapPoint(snapPoints == null ? void 0 : snapPoints[Math.max(newSnapPointIndex, 0)]);\n    }, [\n        drawerRef.current,\n        snapPoints,\n        snapPointsOffset,\n        fadeFromIndex,\n        overlayRef,\n        setActiveSnapPoint\n    ]);\n    React__default.useEffect(()=>{\n        if (activeSnapPoint || activeSnapPointProp) {\n            var _snapPoints_findIndex;\n            const newIndex = (_snapPoints_findIndex = snapPoints == null ? void 0 : snapPoints.findIndex((snapPoint)=>snapPoint === activeSnapPointProp || snapPoint === activeSnapPoint)) != null ? _snapPoints_findIndex : -1;\n            if (snapPointsOffset && newIndex !== -1 && typeof snapPointsOffset[newIndex] === 'number') {\n                snapToPoint(snapPointsOffset[newIndex]);\n            }\n        }\n    }, [\n        activeSnapPoint,\n        activeSnapPointProp,\n        snapPoints,\n        snapPointsOffset,\n        snapToPoint\n    ]);\n    function onRelease({ draggedDistance, closeDrawer, velocity, dismissible }) {\n        if (fadeFromIndex === undefined) return;\n        const currentPosition = direction === 'bottom' || direction === 'right' ? (activeSnapPointOffset != null ? activeSnapPointOffset : 0) - draggedDistance : (activeSnapPointOffset != null ? activeSnapPointOffset : 0) + draggedDistance;\n        const isOverlaySnapPoint = activeSnapPointIndex === fadeFromIndex - 1;\n        const isFirst = activeSnapPointIndex === 0;\n        const hasDraggedUp = draggedDistance > 0;\n        if (isOverlaySnapPoint) {\n            set(overlayRef.current, {\n                transition: `opacity ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(',')})`\n            });\n        }\n        if (!snapToSequentialPoint && velocity > 2 && !hasDraggedUp) {\n            if (dismissible) closeDrawer();\n            else snapToPoint(snapPointsOffset[0]); // snap to initial point\n            return;\n        }\n        if (!snapToSequentialPoint && velocity > 2 && hasDraggedUp && snapPointsOffset && snapPoints) {\n            snapToPoint(snapPointsOffset[snapPoints.length - 1]);\n            return;\n        }\n        // Find the closest snap point to the current position\n        const closestSnapPoint = snapPointsOffset == null ? void 0 : snapPointsOffset.reduce((prev, curr)=>{\n            if (typeof prev !== 'number' || typeof curr !== 'number') return prev;\n            return Math.abs(curr - currentPosition) < Math.abs(prev - currentPosition) ? curr : prev;\n        });\n        const dim = isVertical(direction) ? window.innerHeight : window.innerWidth;\n        if (velocity > VELOCITY_THRESHOLD && Math.abs(draggedDistance) < dim * 0.4) {\n            const dragDirection = hasDraggedUp ? 1 : -1; // 1 = up, -1 = down\n            // Don't do anything if we swipe upwards while being on the last snap point\n            if (dragDirection > 0 && isLastSnapPoint) {\n                snapToPoint(snapPointsOffset[snapPoints.length - 1]);\n                return;\n            }\n            if (isFirst && dragDirection < 0 && dismissible) {\n                closeDrawer();\n            }\n            if (activeSnapPointIndex === null) return;\n            snapToPoint(snapPointsOffset[activeSnapPointIndex + dragDirection]);\n            return;\n        }\n        snapToPoint(closestSnapPoint);\n    }\n    function onDrag({ draggedDistance }) {\n        if (activeSnapPointOffset === null) return;\n        const newValue = direction === 'bottom' || direction === 'right' ? activeSnapPointOffset - draggedDistance : activeSnapPointOffset + draggedDistance;\n        // Don't do anything if we exceed the last(biggest) snap point\n        if ((direction === 'bottom' || direction === 'right') && newValue < snapPointsOffset[snapPointsOffset.length - 1]) {\n            return;\n        }\n        if ((direction === 'top' || direction === 'left') && newValue > snapPointsOffset[snapPointsOffset.length - 1]) {\n            return;\n        }\n        set(drawerRef.current, {\n            transform: isVertical(direction) ? `translate3d(0, ${newValue}px, 0)` : `translate3d(${newValue}px, 0, 0)`\n        });\n    }\n    function getPercentageDragged(absDraggedDistance, isDraggingDown) {\n        if (!snapPoints || typeof activeSnapPointIndex !== 'number' || !snapPointsOffset || fadeFromIndex === undefined) return null;\n        // If this is true we are dragging to a snap point that is supposed to have an overlay\n        const isOverlaySnapPoint = activeSnapPointIndex === fadeFromIndex - 1;\n        const isOverlaySnapPointOrHigher = activeSnapPointIndex >= fadeFromIndex;\n        if (isOverlaySnapPointOrHigher && isDraggingDown) {\n            return 0;\n        }\n        // Don't animate, but still use this one if we are dragging away from the overlaySnapPoint\n        if (isOverlaySnapPoint && !isDraggingDown) return 1;\n        if (!shouldFade && !isOverlaySnapPoint) return null;\n        // Either fadeFrom index or the one before\n        const targetSnapPointIndex = isOverlaySnapPoint ? activeSnapPointIndex + 1 : activeSnapPointIndex - 1;\n        // Get the distance from overlaySnapPoint to the one before or vice-versa to calculate the opacity percentage accordingly\n        const snapPointDistance = isOverlaySnapPoint ? snapPointsOffset[targetSnapPointIndex] - snapPointsOffset[targetSnapPointIndex - 1] : snapPointsOffset[targetSnapPointIndex + 1] - snapPointsOffset[targetSnapPointIndex];\n        const percentageDragged = absDraggedDistance / Math.abs(snapPointDistance);\n        if (isOverlaySnapPoint) {\n            return 1 - percentageDragged;\n        } else {\n            return percentageDragged;\n        }\n    }\n    return {\n        isLastSnapPoint,\n        activeSnapPoint,\n        shouldFade,\n        getPercentageDragged,\n        setActiveSnapPoint,\n        activeSnapPointIndex,\n        onRelease,\n        onDrag,\n        snapPointsOffset\n    };\n}\n\nconst noop = ()=>()=>{};\nfunction useScaleBackground() {\n    const { direction, isOpen, shouldScaleBackground, setBackgroundColorOnScale, noBodyStyles } = useDrawerContext();\n    const timeoutIdRef = React__default.useRef(null);\n    const initialBackgroundColor = useMemo(()=>document.body.style.backgroundColor, []);\n    function getScale() {\n        return (window.innerWidth - WINDOW_TOP_OFFSET) / window.innerWidth;\n    }\n    React__default.useEffect(()=>{\n        if (isOpen && shouldScaleBackground) {\n            if (timeoutIdRef.current) clearTimeout(timeoutIdRef.current);\n            const wrapper = document.querySelector('[data-vaul-drawer-wrapper]') || document.querySelector('[vaul-drawer-wrapper]');\n            if (!wrapper) return;\n            chain(setBackgroundColorOnScale && !noBodyStyles ? assignStyle(document.body, {\n                background: 'black'\n            }) : noop, assignStyle(wrapper, {\n                transformOrigin: isVertical(direction) ? 'top' : 'left',\n                transitionProperty: 'transform, border-radius',\n                transitionDuration: `${TRANSITIONS.DURATION}s`,\n                transitionTimingFunction: `cubic-bezier(${TRANSITIONS.EASE.join(',')})`\n            }));\n            const wrapperStylesCleanup = assignStyle(wrapper, {\n                borderRadius: `${BORDER_RADIUS}px`,\n                overflow: 'hidden',\n                ...isVertical(direction) ? {\n                    transform: `scale(${getScale()}) translate3d(0, calc(env(safe-area-inset-top) + 14px), 0)`\n                } : {\n                    transform: `scale(${getScale()}) translate3d(calc(env(safe-area-inset-top) + 14px), 0, 0)`\n                }\n            });\n            return ()=>{\n                wrapperStylesCleanup();\n                timeoutIdRef.current = window.setTimeout(()=>{\n                    if (initialBackgroundColor) {\n                        document.body.style.background = initialBackgroundColor;\n                    } else {\n                        document.body.style.removeProperty('background');\n                    }\n                }, TRANSITIONS.DURATION * 1000);\n            };\n        }\n    }, [\n        isOpen,\n        shouldScaleBackground,\n        initialBackgroundColor\n    ]);\n}\n\nlet previousBodyPosition = null;\n/**\n * This hook is necessary to prevent buggy behavior on iOS devices (need to test on Android).\n * I won't get into too much detail about what bugs it solves, but so far I've found that setting the body to `position: fixed` is the most reliable way to prevent those bugs.\n * Issues that this hook solves:\n * https://github.com/emilkowalski/vaul/issues/435\n * https://github.com/emilkowalski/vaul/issues/433\n * And more that I discovered, but were just not reported.\n */ function usePositionFixed({ isOpen, modal, nested, hasBeenOpened, preventScrollRestoration, noBodyStyles }) {\n    const [activeUrl, setActiveUrl] = React__default.useState(()=>typeof window !== 'undefined' ? window.location.href : '');\n    const scrollPos = React__default.useRef(0);\n    const setPositionFixed = React__default.useCallback(()=>{\n        // All browsers on iOS will return true here.\n        if (!isSafari()) return;\n        // If previousBodyPosition is already set, don't set it again.\n        if (previousBodyPosition === null && isOpen && !noBodyStyles) {\n            previousBodyPosition = {\n                position: document.body.style.position,\n                top: document.body.style.top,\n                left: document.body.style.left,\n                height: document.body.style.height,\n                right: 'unset'\n            };\n            // Update the dom inside an animation frame\n            const { scrollX, innerHeight } = window;\n            document.body.style.setProperty('position', 'fixed', 'important');\n            Object.assign(document.body.style, {\n                top: `${-scrollPos.current}px`,\n                left: `${-scrollX}px`,\n                right: '0px',\n                height: 'auto'\n            });\n            window.setTimeout(()=>window.requestAnimationFrame(()=>{\n                    // Attempt to check if the bottom bar appeared due to the position change\n                    const bottomBarHeight = innerHeight - window.innerHeight;\n                    if (bottomBarHeight && scrollPos.current >= innerHeight) {\n                        // Move the content further up so that the bottom bar doesn't hide it\n                        document.body.style.top = `${-(scrollPos.current + bottomBarHeight)}px`;\n                    }\n                }), 300);\n        }\n    }, [\n        isOpen\n    ]);\n    const restorePositionSetting = React__default.useCallback(()=>{\n        // All browsers on iOS will return true here.\n        if (!isSafari()) return;\n        if (previousBodyPosition !== null && !noBodyStyles) {\n            // Convert the position from \"px\" to Int\n            const y = -parseInt(document.body.style.top, 10);\n            const x = -parseInt(document.body.style.left, 10);\n            // Restore styles\n            Object.assign(document.body.style, previousBodyPosition);\n            window.requestAnimationFrame(()=>{\n                if (preventScrollRestoration && activeUrl !== window.location.href) {\n                    setActiveUrl(window.location.href);\n                    return;\n                }\n                window.scrollTo(x, y);\n            });\n            previousBodyPosition = null;\n        }\n    }, [\n        activeUrl\n    ]);\n    React__default.useEffect(()=>{\n        function onScroll() {\n            scrollPos.current = window.scrollY;\n        }\n        onScroll();\n        window.addEventListener('scroll', onScroll);\n        return ()=>{\n            window.removeEventListener('scroll', onScroll);\n        };\n    }, []);\n    React__default.useEffect(()=>{\n        if (nested || !hasBeenOpened) return;\n        // This is needed to force Safari toolbar to show **before** the drawer starts animating to prevent a gnarly shift from happening\n        if (isOpen) {\n            // avoid for standalone mode (PWA)\n            const isStandalone = window.matchMedia('(display-mode: standalone)').matches;\n            !isStandalone && setPositionFixed();\n            if (!modal) {\n                window.setTimeout(()=>{\n                    restorePositionSetting();\n                }, 500);\n            }\n        } else {\n            restorePositionSetting();\n        }\n    }, [\n        isOpen,\n        hasBeenOpened,\n        activeUrl,\n        modal,\n        nested,\n        setPositionFixed,\n        restorePositionSetting\n    ]);\n    return {\n        restorePositionSetting\n    };\n}\n\nfunction Root({ open: openProp, onOpenChange, children, onDrag: onDragProp, onRelease: onReleaseProp, snapPoints, shouldScaleBackground = false, setBackgroundColorOnScale = true, closeThreshold = CLOSE_THRESHOLD, scrollLockTimeout = SCROLL_LOCK_TIMEOUT, dismissible = true, handleOnly = false, fadeFromIndex = snapPoints && snapPoints.length - 1, activeSnapPoint: activeSnapPointProp, setActiveSnapPoint: setActiveSnapPointProp, fixed, modal = true, onClose, nested, noBodyStyles, direction = 'bottom', defaultOpen = false, disablePreventScroll = true, snapToSequentialPoint = false, preventScrollRestoration = false, repositionInputs = true, onAnimationEnd, container, autoFocus = false }) {\n    var _drawerRef_current, _drawerRef_current1;\n    const [isOpen = false, setIsOpen] = useControllableState({\n        defaultProp: defaultOpen,\n        prop: openProp,\n        onChange: (o)=>{\n            onOpenChange == null ? void 0 : onOpenChange(o);\n            if (!o && !nested) {\n                restorePositionSetting();\n            }\n            setTimeout(()=>{\n                onAnimationEnd == null ? void 0 : onAnimationEnd(o);\n            }, TRANSITIONS.DURATION * 1000);\n            if (o && !modal) {\n                if (typeof window !== 'undefined') {\n                    window.requestAnimationFrame(()=>{\n                        document.body.style.pointerEvents = 'auto';\n                    });\n                }\n            }\n            if (!o) {\n                // This will be removed when the exit animation ends (`500ms`)\n                document.body.style.pointerEvents = 'auto';\n            }\n        }\n    });\n    const [hasBeenOpened, setHasBeenOpened] = React__default.useState(false);\n    const [isDragging, setIsDragging] = React__default.useState(false);\n    const [justReleased, setJustReleased] = React__default.useState(false);\n    const overlayRef = React__default.useRef(null);\n    const openTime = React__default.useRef(null);\n    const dragStartTime = React__default.useRef(null);\n    const dragEndTime = React__default.useRef(null);\n    const lastTimeDragPrevented = React__default.useRef(null);\n    const isAllowedToDrag = React__default.useRef(false);\n    const nestedOpenChangeTimer = React__default.useRef(null);\n    const pointerStart = React__default.useRef(0);\n    const keyboardIsOpen = React__default.useRef(false);\n    const previousDiffFromInitial = React__default.useRef(0);\n    const drawerRef = React__default.useRef(null);\n    const drawerHeightRef = React__default.useRef(((_drawerRef_current = drawerRef.current) == null ? void 0 : _drawerRef_current.getBoundingClientRect().height) || 0);\n    const drawerWidthRef = React__default.useRef(((_drawerRef_current1 = drawerRef.current) == null ? void 0 : _drawerRef_current1.getBoundingClientRect().width) || 0);\n    const initialDrawerHeight = React__default.useRef(0);\n    const onSnapPointChange = React__default.useCallback((activeSnapPointIndex)=>{\n        // Change openTime ref when we reach the last snap point to prevent dragging for 500ms incase it's scrollable.\n        if (snapPoints && activeSnapPointIndex === snapPointsOffset.length - 1) openTime.current = new Date();\n    }, []);\n    const { activeSnapPoint, activeSnapPointIndex, setActiveSnapPoint, onRelease: onReleaseSnapPoints, snapPointsOffset, onDrag: onDragSnapPoints, shouldFade, getPercentageDragged: getSnapPointsPercentageDragged } = useSnapPoints({\n        snapPoints,\n        activeSnapPointProp,\n        setActiveSnapPointProp,\n        drawerRef,\n        fadeFromIndex,\n        overlayRef,\n        onSnapPointChange,\n        direction,\n        container,\n        snapToSequentialPoint\n    });\n    usePreventScroll({\n        isDisabled: !isOpen || isDragging || !modal || justReleased || !hasBeenOpened || !repositionInputs || !disablePreventScroll\n    });\n    const { restorePositionSetting } = usePositionFixed({\n        isOpen,\n        modal,\n        nested,\n        hasBeenOpened,\n        preventScrollRestoration,\n        noBodyStyles\n    });\n    function getScale() {\n        return (window.innerWidth - WINDOW_TOP_OFFSET) / window.innerWidth;\n    }\n    function onPress(event) {\n        var _drawerRef_current, _drawerRef_current1;\n        if (!dismissible && !snapPoints) return;\n        if (drawerRef.current && !drawerRef.current.contains(event.target)) return;\n        drawerHeightRef.current = ((_drawerRef_current = drawerRef.current) == null ? void 0 : _drawerRef_current.getBoundingClientRect().height) || 0;\n        drawerWidthRef.current = ((_drawerRef_current1 = drawerRef.current) == null ? void 0 : _drawerRef_current1.getBoundingClientRect().width) || 0;\n        setIsDragging(true);\n        dragStartTime.current = new Date();\n        // iOS doesn't trigger mouseUp after scrolling so we need to listen to touched in order to disallow dragging\n        if (isIOS()) {\n            window.addEventListener('touchend', ()=>isAllowedToDrag.current = false, {\n                once: true\n            });\n        }\n        // Ensure we maintain correct pointer capture even when going outside of the drawer\n        event.target.setPointerCapture(event.pointerId);\n        pointerStart.current = isVertical(direction) ? event.pageY : event.pageX;\n    }\n    function shouldDrag(el, isDraggingInDirection) {\n        var _window_getSelection, _lastTimeDragPrevented_current;\n        let element = el;\n        const highlightedText = (_window_getSelection = window.getSelection()) == null ? void 0 : _window_getSelection.toString();\n        const swipeAmount = drawerRef.current ? getTranslate(drawerRef.current, direction) : null;\n        const date = new Date();\n        if (element.hasAttribute('data-vaul-no-drag') || element.closest('[data-vaul-no-drag]')) {\n            return false;\n        }\n        if (direction === 'right' || direction === 'left') {\n            return true;\n        }\n        // Allow scrolling when animating\n        if (openTime.current && date.getTime() - openTime.current.getTime() < 500) {\n            return false;\n        }\n        if (swipeAmount !== null) {\n            if (direction === 'bottom' ? swipeAmount > 0 : swipeAmount < 0) {\n                return true;\n            }\n        }\n        // Don't drag if there's highlighted text\n        if (highlightedText && highlightedText.length > 0) {\n            return false;\n        }\n        // Disallow dragging if drawer was scrolled within `scrollLockTimeout`\n        if (date.getTime() - ((_lastTimeDragPrevented_current = lastTimeDragPrevented.current) == null ? void 0 : _lastTimeDragPrevented_current.getTime()) < scrollLockTimeout && swipeAmount === 0) {\n            lastTimeDragPrevented.current = date;\n            return false;\n        }\n        if (isDraggingInDirection) {\n            lastTimeDragPrevented.current = date;\n            // We are dragging down so we should allow scrolling\n            return false;\n        }\n        // Keep climbing up the DOM tree as long as there's a parent\n        while(element){\n            // Check if the element is scrollable\n            if (element.scrollHeight > element.clientHeight) {\n                if (element.scrollTop !== 0) {\n                    lastTimeDragPrevented.current = new Date();\n                    // The element is scrollable and not scrolled to the top, so don't drag\n                    return false;\n                }\n                if (element.getAttribute('role') === 'dialog') {\n                    return true;\n                }\n            }\n            // Move up to the parent element\n            element = element.parentNode;\n        }\n        // No scrollable parents not scrolled to the top found, so drag\n        return true;\n    }\n    function onDrag(event) {\n        if (!drawerRef.current) {\n            return;\n        }\n        // We need to know how much of the drawer has been dragged in percentages so that we can transform background accordingly\n        if (isDragging) {\n            const directionMultiplier = direction === 'bottom' || direction === 'right' ? 1 : -1;\n            const draggedDistance = (pointerStart.current - (isVertical(direction) ? event.pageY : event.pageX)) * directionMultiplier;\n            const isDraggingInDirection = draggedDistance > 0;\n            // Pre condition for disallowing dragging in the close direction.\n            const noCloseSnapPointsPreCondition = snapPoints && !dismissible && !isDraggingInDirection;\n            // Disallow dragging down to close when first snap point is the active one and dismissible prop is set to false.\n            if (noCloseSnapPointsPreCondition && activeSnapPointIndex === 0) return;\n            // We need to capture last time when drag with scroll was triggered and have a timeout between\n            const absDraggedDistance = Math.abs(draggedDistance);\n            const wrapper = document.querySelector('[data-vaul-drawer-wrapper]');\n            const drawerDimension = direction === 'bottom' || direction === 'top' ? drawerHeightRef.current : drawerWidthRef.current;\n            // Calculate the percentage dragged, where 1 is the closed position\n            let percentageDragged = absDraggedDistance / drawerDimension;\n            const snapPointPercentageDragged = getSnapPointsPercentageDragged(absDraggedDistance, isDraggingInDirection);\n            if (snapPointPercentageDragged !== null) {\n                percentageDragged = snapPointPercentageDragged;\n            }\n            // Disallow close dragging beyond the smallest snap point.\n            if (noCloseSnapPointsPreCondition && percentageDragged >= 1) {\n                return;\n            }\n            if (!isAllowedToDrag.current && !shouldDrag(event.target, isDraggingInDirection)) return;\n            drawerRef.current.classList.add(DRAG_CLASS);\n            // If shouldDrag gave true once after pressing down on the drawer, we set isAllowedToDrag to true and it will remain true until we let go, there's no reason to disable dragging mid way, ever, and that's the solution to it\n            isAllowedToDrag.current = true;\n            set(drawerRef.current, {\n                transition: 'none'\n            });\n            set(overlayRef.current, {\n                transition: 'none'\n            });\n            if (snapPoints) {\n                onDragSnapPoints({\n                    draggedDistance\n                });\n            }\n            // Run this only if snapPoints are not defined or if we are at the last snap point (highest one)\n            if (isDraggingInDirection && !snapPoints) {\n                const dampenedDraggedDistance = dampenValue(draggedDistance);\n                const translateValue = Math.min(dampenedDraggedDistance * -1, 0) * directionMultiplier;\n                set(drawerRef.current, {\n                    transform: isVertical(direction) ? `translate3d(0, ${translateValue}px, 0)` : `translate3d(${translateValue}px, 0, 0)`\n                });\n                return;\n            }\n            const opacityValue = 1 - percentageDragged;\n            if (shouldFade || fadeFromIndex && activeSnapPointIndex === fadeFromIndex - 1) {\n                onDragProp == null ? void 0 : onDragProp(event, percentageDragged);\n                set(overlayRef.current, {\n                    opacity: `${opacityValue}`,\n                    transition: 'none'\n                }, true);\n            }\n            if (wrapper && overlayRef.current && shouldScaleBackground) {\n                // Calculate percentageDragged as a fraction (0 to 1)\n                const scaleValue = Math.min(getScale() + percentageDragged * (1 - getScale()), 1);\n                const borderRadiusValue = 8 - percentageDragged * 8;\n                const translateValue = Math.max(0, 14 - percentageDragged * 14);\n                set(wrapper, {\n                    borderRadius: `${borderRadiusValue}px`,\n                    transform: isVertical(direction) ? `scale(${scaleValue}) translate3d(0, ${translateValue}px, 0)` : `scale(${scaleValue}) translate3d(${translateValue}px, 0, 0)`,\n                    transition: 'none'\n                }, true);\n            }\n            if (!snapPoints) {\n                const translateValue = absDraggedDistance * directionMultiplier;\n                set(drawerRef.current, {\n                    transform: isVertical(direction) ? `translate3d(0, ${translateValue}px, 0)` : `translate3d(${translateValue}px, 0, 0)`\n                });\n            }\n        }\n    }\n    React__default.useEffect(()=>{\n        var _window_visualViewport;\n        function onVisualViewportChange() {\n            if (!drawerRef.current || !repositionInputs) return;\n            const focusedElement = document.activeElement;\n            if (isInput(focusedElement) || keyboardIsOpen.current) {\n                var _window_visualViewport;\n                const visualViewportHeight = ((_window_visualViewport = window.visualViewport) == null ? void 0 : _window_visualViewport.height) || 0;\n                const totalHeight = window.innerHeight;\n                // This is the height of the keyboard\n                let diffFromInitial = totalHeight - visualViewportHeight;\n                const drawerHeight = drawerRef.current.getBoundingClientRect().height || 0;\n                // Adjust drawer height only if it's tall enough\n                const isTallEnough = drawerHeight > totalHeight * 0.8;\n                if (!initialDrawerHeight.current) {\n                    initialDrawerHeight.current = drawerHeight;\n                }\n                const offsetFromTop = drawerRef.current.getBoundingClientRect().top;\n                // visualViewport height may change due to somq e subtle changes to the keyboard. Checking if the height changed by 60 or more will make sure that they keyboard really changed its open state.\n                if (Math.abs(previousDiffFromInitial.current - diffFromInitial) > 60) {\n                    keyboardIsOpen.current = !keyboardIsOpen.current;\n                }\n                if (snapPoints && snapPoints.length > 0 && snapPointsOffset && activeSnapPointIndex) {\n                    const activeSnapPointHeight = snapPointsOffset[activeSnapPointIndex] || 0;\n                    diffFromInitial += activeSnapPointHeight;\n                }\n                previousDiffFromInitial.current = diffFromInitial;\n                // We don't have to change the height if the input is in view, when we are here we are in the opened keyboard state so we can correctly check if the input is in view\n                if (drawerHeight > visualViewportHeight || keyboardIsOpen.current) {\n                    const height = drawerRef.current.getBoundingClientRect().height;\n                    let newDrawerHeight = height;\n                    if (height > visualViewportHeight) {\n                        newDrawerHeight = visualViewportHeight - (isTallEnough ? offsetFromTop : WINDOW_TOP_OFFSET);\n                    }\n                    // When fixed, don't move the drawer upwards if there's space, but rather only change it's height so it's fully scrollable when the keyboard is open\n                    if (fixed) {\n                        drawerRef.current.style.height = `${height - Math.max(diffFromInitial, 0)}px`;\n                    } else {\n                        drawerRef.current.style.height = `${Math.max(newDrawerHeight, visualViewportHeight - offsetFromTop)}px`;\n                    }\n                } else {\n                    drawerRef.current.style.height = `${initialDrawerHeight.current}px`;\n                }\n                if (snapPoints && snapPoints.length > 0 && !keyboardIsOpen.current) {\n                    drawerRef.current.style.bottom = `0px`;\n                } else {\n                    // Negative bottom value would never make sense\n                    drawerRef.current.style.bottom = `${Math.max(diffFromInitial, 0)}px`;\n                }\n            }\n        }\n        (_window_visualViewport = window.visualViewport) == null ? void 0 : _window_visualViewport.addEventListener('resize', onVisualViewportChange);\n        return ()=>{\n            var _window_visualViewport;\n            return (_window_visualViewport = window.visualViewport) == null ? void 0 : _window_visualViewport.removeEventListener('resize', onVisualViewportChange);\n        };\n    }, [\n        activeSnapPointIndex,\n        snapPoints,\n        snapPointsOffset\n    ]);\n    function closeDrawer(fromWithin) {\n        cancelDrag();\n        onClose == null ? void 0 : onClose();\n        if (!fromWithin) {\n            setIsOpen(false);\n        }\n        setTimeout(()=>{\n            if (snapPoints) {\n                setActiveSnapPoint(snapPoints[0]);\n            }\n        }, TRANSITIONS.DURATION * 1000); // seconds to ms\n    }\n    function resetDrawer() {\n        if (!drawerRef.current) return;\n        const wrapper = document.querySelector('[data-vaul-drawer-wrapper]');\n        const currentSwipeAmount = getTranslate(drawerRef.current, direction);\n        set(drawerRef.current, {\n            transform: 'translate3d(0, 0, 0)',\n            transition: `transform ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(',')})`\n        });\n        set(overlayRef.current, {\n            transition: `opacity ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(',')})`,\n            opacity: '1'\n        });\n        // Don't reset background if swiped upwards\n        if (shouldScaleBackground && currentSwipeAmount && currentSwipeAmount > 0 && isOpen) {\n            set(wrapper, {\n                borderRadius: `${BORDER_RADIUS}px`,\n                overflow: 'hidden',\n                ...isVertical(direction) ? {\n                    transform: `scale(${getScale()}) translate3d(0, calc(env(safe-area-inset-top) + 14px), 0)`,\n                    transformOrigin: 'top'\n                } : {\n                    transform: `scale(${getScale()}) translate3d(calc(env(safe-area-inset-top) + 14px), 0, 0)`,\n                    transformOrigin: 'left'\n                },\n                transitionProperty: 'transform, border-radius',\n                transitionDuration: `${TRANSITIONS.DURATION}s`,\n                transitionTimingFunction: `cubic-bezier(${TRANSITIONS.EASE.join(',')})`\n            }, true);\n        }\n    }\n    function cancelDrag() {\n        if (!isDragging || !drawerRef.current) return;\n        drawerRef.current.classList.remove(DRAG_CLASS);\n        isAllowedToDrag.current = false;\n        setIsDragging(false);\n        dragEndTime.current = new Date();\n    }\n    function onRelease(event) {\n        if (!isDragging || !drawerRef.current) return;\n        drawerRef.current.classList.remove(DRAG_CLASS);\n        isAllowedToDrag.current = false;\n        setIsDragging(false);\n        dragEndTime.current = new Date();\n        const swipeAmount = getTranslate(drawerRef.current, direction);\n        if (!shouldDrag(event.target, false) || !swipeAmount || Number.isNaN(swipeAmount)) return;\n        if (dragStartTime.current === null) return;\n        const timeTaken = dragEndTime.current.getTime() - dragStartTime.current.getTime();\n        const distMoved = pointerStart.current - (isVertical(direction) ? event.pageY : event.pageX);\n        const velocity = Math.abs(distMoved) / timeTaken;\n        if (velocity > 0.05) {\n            // `justReleased` is needed to prevent the drawer from focusing on an input when the drag ends, as it's not the intent most of the time.\n            setJustReleased(true);\n            setTimeout(()=>{\n                setJustReleased(false);\n            }, 200);\n        }\n        if (snapPoints) {\n            const directionMultiplier = direction === 'bottom' || direction === 'right' ? 1 : -1;\n            onReleaseSnapPoints({\n                draggedDistance: distMoved * directionMultiplier,\n                closeDrawer,\n                velocity,\n                dismissible\n            });\n            onReleaseProp == null ? void 0 : onReleaseProp(event, true);\n            return;\n        }\n        // Moved upwards, don't do anything\n        if (direction === 'bottom' || direction === 'right' ? distMoved > 0 : distMoved < 0) {\n            resetDrawer();\n            onReleaseProp == null ? void 0 : onReleaseProp(event, true);\n            return;\n        }\n        if (velocity > VELOCITY_THRESHOLD) {\n            closeDrawer();\n            onReleaseProp == null ? void 0 : onReleaseProp(event, false);\n            return;\n        }\n        var _drawerRef_current_getBoundingClientRect_height;\n        const visibleDrawerHeight = Math.min((_drawerRef_current_getBoundingClientRect_height = drawerRef.current.getBoundingClientRect().height) != null ? _drawerRef_current_getBoundingClientRect_height : 0, window.innerHeight);\n        var _drawerRef_current_getBoundingClientRect_width;\n        const visibleDrawerWidth = Math.min((_drawerRef_current_getBoundingClientRect_width = drawerRef.current.getBoundingClientRect().width) != null ? _drawerRef_current_getBoundingClientRect_width : 0, window.innerWidth);\n        const isHorizontalSwipe = direction === 'left' || direction === 'right';\n        if (Math.abs(swipeAmount) >= (isHorizontalSwipe ? visibleDrawerWidth : visibleDrawerHeight) * closeThreshold) {\n            closeDrawer();\n            onReleaseProp == null ? void 0 : onReleaseProp(event, false);\n            return;\n        }\n        onReleaseProp == null ? void 0 : onReleaseProp(event, true);\n        resetDrawer();\n    }\n    React__default.useEffect(()=>{\n        // Trigger enter animation without using CSS animation\n        if (isOpen) {\n            set(document.documentElement, {\n                scrollBehavior: 'auto'\n            });\n            openTime.current = new Date();\n        }\n        return ()=>{\n            reset(document.documentElement, 'scrollBehavior');\n        };\n    }, [\n        isOpen\n    ]);\n    function onNestedOpenChange(o) {\n        const scale = o ? (window.innerWidth - NESTED_DISPLACEMENT) / window.innerWidth : 1;\n        const y = o ? -NESTED_DISPLACEMENT : 0;\n        if (nestedOpenChangeTimer.current) {\n            window.clearTimeout(nestedOpenChangeTimer.current);\n        }\n        set(drawerRef.current, {\n            transition: `transform ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(',')})`,\n            transform: `scale(${scale}) translate3d(0, ${y}px, 0)`\n        });\n        if (!o && drawerRef.current) {\n            nestedOpenChangeTimer.current = setTimeout(()=>{\n                const translateValue = getTranslate(drawerRef.current, direction);\n                set(drawerRef.current, {\n                    transition: 'none',\n                    transform: isVertical(direction) ? `translate3d(0, ${translateValue}px, 0)` : `translate3d(${translateValue}px, 0, 0)`\n                });\n            }, 500);\n        }\n    }\n    function onNestedDrag(_event, percentageDragged) {\n        if (percentageDragged < 0) return;\n        const initialScale = (window.innerWidth - NESTED_DISPLACEMENT) / window.innerWidth;\n        const newScale = initialScale + percentageDragged * (1 - initialScale);\n        const newTranslate = -NESTED_DISPLACEMENT + percentageDragged * NESTED_DISPLACEMENT;\n        set(drawerRef.current, {\n            transform: isVertical(direction) ? `scale(${newScale}) translate3d(0, ${newTranslate}px, 0)` : `scale(${newScale}) translate3d(${newTranslate}px, 0, 0)`,\n            transition: 'none'\n        });\n    }\n    function onNestedRelease(_event, o) {\n        const dim = isVertical(direction) ? window.innerHeight : window.innerWidth;\n        const scale = o ? (dim - NESTED_DISPLACEMENT) / dim : 1;\n        const translate = o ? -NESTED_DISPLACEMENT : 0;\n        if (o) {\n            set(drawerRef.current, {\n                transition: `transform ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(',')})`,\n                transform: isVertical(direction) ? `scale(${scale}) translate3d(0, ${translate}px, 0)` : `scale(${scale}) translate3d(${translate}px, 0, 0)`\n            });\n        }\n    }\n    return /*#__PURE__*/ React__default.createElement(DialogPrimitive.Root, {\n        defaultOpen: defaultOpen,\n        onOpenChange: (open)=>{\n            if (!dismissible && !open) return;\n            if (open) {\n                setHasBeenOpened(true);\n            } else {\n                closeDrawer(true);\n            }\n            setIsOpen(open);\n        },\n        open: isOpen\n    }, /*#__PURE__*/ React__default.createElement(DrawerContext.Provider, {\n        value: {\n            activeSnapPoint,\n            snapPoints,\n            setActiveSnapPoint,\n            drawerRef,\n            overlayRef,\n            onOpenChange,\n            onPress,\n            onRelease,\n            onDrag,\n            dismissible,\n            handleOnly,\n            isOpen,\n            isDragging,\n            shouldFade,\n            closeDrawer,\n            onNestedDrag,\n            onNestedOpenChange,\n            onNestedRelease,\n            keyboardIsOpen,\n            modal,\n            snapPointsOffset,\n            direction,\n            shouldScaleBackground,\n            setBackgroundColorOnScale,\n            noBodyStyles,\n            container,\n            autoFocus\n        }\n    }, children));\n}\nconst Overlay = /*#__PURE__*/ React__default.forwardRef(function({ ...rest }, ref) {\n    const { overlayRef, snapPoints, onRelease, shouldFade, isOpen, modal } = useDrawerContext();\n    const composedRef = useComposedRefs(ref, overlayRef);\n    const hasSnapPoints = snapPoints && snapPoints.length > 0;\n    // Overlay is the component that is locking scroll, removing it will unlock the scroll without having to dig into Radix's Dialog library\n    if (!modal) {\n        // Need to do this manually unfortunately\n        if (typeof window !== 'undefined') {\n            window.requestAnimationFrame(()=>{\n                document.body.style.pointerEvents = 'auto';\n            });\n        }\n        return null;\n    }\n    return /*#__PURE__*/ React__default.createElement(DialogPrimitive.Overlay, {\n        onMouseUp: onRelease,\n        ref: composedRef,\n        \"data-vaul-overlay\": \"\",\n        \"data-vaul-snap-points\": isOpen && hasSnapPoints ? 'true' : 'false',\n        \"data-vaul-snap-points-overlay\": isOpen && shouldFade ? 'true' : 'false',\n        ...rest\n    });\n});\nOverlay.displayName = 'Drawer.Overlay';\nconst Content = /*#__PURE__*/ React__default.forwardRef(function({ onPointerDownOutside, style, onOpenAutoFocus, ...rest }, ref) {\n    const { drawerRef, onPress, onRelease, onDrag, keyboardIsOpen, snapPointsOffset, modal, isOpen, direction, snapPoints, container, handleOnly, autoFocus } = useDrawerContext();\n    // Needed to use transition instead of animations\n    const [delayedSnapPoints, setDelayedSnapPoints] = React__default.useState(false);\n    const composedRef = useComposedRefs(ref, drawerRef);\n    const pointerStartRef = React__default.useRef(null);\n    const lastKnownPointerEventRef = React__default.useRef(null);\n    const wasBeyondThePointRef = React__default.useRef(false);\n    const hasSnapPoints = snapPoints && snapPoints.length > 0;\n    useScaleBackground();\n    const isDeltaInDirection = (delta, direction, threshold = 0)=>{\n        if (wasBeyondThePointRef.current) return true;\n        const deltaY = Math.abs(delta.y);\n        const deltaX = Math.abs(delta.x);\n        const isDeltaX = deltaX > deltaY;\n        const dFactor = [\n            'bottom',\n            'right'\n        ].includes(direction) ? 1 : -1;\n        if (direction === 'left' || direction === 'right') {\n            const isReverseDirection = delta.x * dFactor < 0;\n            if (!isReverseDirection && deltaX >= 0 && deltaX <= threshold) {\n                return isDeltaX;\n            }\n        } else {\n            const isReverseDirection = delta.y * dFactor < 0;\n            if (!isReverseDirection && deltaY >= 0 && deltaY <= threshold) {\n                return !isDeltaX;\n            }\n        }\n        wasBeyondThePointRef.current = true;\n        return true;\n    };\n    React__default.useEffect(()=>{\n        if (hasSnapPoints) {\n            window.requestAnimationFrame(()=>{\n                setDelayedSnapPoints(true);\n            });\n        }\n    }, []);\n    function handleOnPointerUp(event) {\n        pointerStartRef.current = null;\n        wasBeyondThePointRef.current = false;\n        onRelease(event);\n    }\n    return /*#__PURE__*/ React__default.createElement(DialogPrimitive.Content, {\n        \"data-vaul-drawer-direction\": direction,\n        \"data-vaul-drawer\": \"\",\n        \"data-vaul-delayed-snap-points\": delayedSnapPoints ? 'true' : 'false',\n        \"data-vaul-snap-points\": isOpen && hasSnapPoints ? 'true' : 'false',\n        \"data-vaul-custom-container\": container ? 'true' : 'false',\n        ...rest,\n        ref: composedRef,\n        style: snapPointsOffset && snapPointsOffset.length > 0 ? {\n            '--snap-point-height': `${snapPointsOffset[0]}px`,\n            ...style\n        } : style,\n        onPointerDown: (event)=>{\n            if (handleOnly) return;\n            rest.onPointerDown == null ? void 0 : rest.onPointerDown.call(rest, event);\n            pointerStartRef.current = {\n                x: event.pageX,\n                y: event.pageY\n            };\n            onPress(event);\n        },\n        onOpenAutoFocus: (e)=>{\n            onOpenAutoFocus == null ? void 0 : onOpenAutoFocus(e);\n            if (!autoFocus) {\n                e.preventDefault();\n            }\n        },\n        onPointerDownOutside: (e)=>{\n            onPointerDownOutside == null ? void 0 : onPointerDownOutside(e);\n            if (!modal || e.defaultPrevented) {\n                e.preventDefault();\n                return;\n            }\n            if (keyboardIsOpen.current) {\n                keyboardIsOpen.current = false;\n            }\n        },\n        onFocusOutside: (e)=>{\n            if (!modal) {\n                e.preventDefault();\n                return;\n            }\n        },\n        onPointerMove: (event)=>{\n            lastKnownPointerEventRef.current = event;\n            if (handleOnly) return;\n            rest.onPointerMove == null ? void 0 : rest.onPointerMove.call(rest, event);\n            if (!pointerStartRef.current) return;\n            const yPosition = event.pageY - pointerStartRef.current.y;\n            const xPosition = event.pageX - pointerStartRef.current.x;\n            const swipeStartThreshold = event.pointerType === 'touch' ? 10 : 2;\n            const delta = {\n                x: xPosition,\n                y: yPosition\n            };\n            const isAllowedToSwipe = isDeltaInDirection(delta, direction, swipeStartThreshold);\n            if (isAllowedToSwipe) onDrag(event);\n            else if (Math.abs(xPosition) > swipeStartThreshold || Math.abs(yPosition) > swipeStartThreshold) {\n                pointerStartRef.current = null;\n            }\n        },\n        onPointerUp: (event)=>{\n            rest.onPointerUp == null ? void 0 : rest.onPointerUp.call(rest, event);\n            pointerStartRef.current = null;\n            wasBeyondThePointRef.current = false;\n            onRelease(event);\n        },\n        onPointerOut: (event)=>{\n            rest.onPointerOut == null ? void 0 : rest.onPointerOut.call(rest, event);\n            handleOnPointerUp(lastKnownPointerEventRef.current);\n        },\n        onContextMenu: (event)=>{\n            rest.onContextMenu == null ? void 0 : rest.onContextMenu.call(rest, event);\n            handleOnPointerUp(lastKnownPointerEventRef.current);\n        }\n    });\n});\nContent.displayName = 'Drawer.Content';\nconst LONG_HANDLE_PRESS_TIMEOUT = 250;\nconst DOUBLE_TAP_TIMEOUT = 120;\nconst Handle = /*#__PURE__*/ React__default.forwardRef(function({ preventCycle = false, children, ...rest }, ref) {\n    const { closeDrawer, isDragging, snapPoints, activeSnapPoint, setActiveSnapPoint, dismissible, handleOnly, isOpen, onPress, onDrag } = useDrawerContext();\n    const closeTimeoutIdRef = React__default.useRef(null);\n    const shouldCancelInteractionRef = React__default.useRef(false);\n    function handleStartCycle() {\n        // Stop if this is the second click of a double click\n        if (shouldCancelInteractionRef.current) {\n            handleCancelInteraction();\n            return;\n        }\n        window.setTimeout(()=>{\n            handleCycleSnapPoints();\n        }, DOUBLE_TAP_TIMEOUT);\n    }\n    function handleCycleSnapPoints() {\n        // Prevent accidental taps while resizing drawer\n        if (isDragging || preventCycle || shouldCancelInteractionRef.current) {\n            handleCancelInteraction();\n            return;\n        }\n        // Make sure to clear the timeout id if the user releases the handle before the cancel timeout\n        handleCancelInteraction();\n        if ((!snapPoints || snapPoints.length === 0) && dismissible) {\n            closeDrawer();\n            return;\n        }\n        const isLastSnapPoint = activeSnapPoint === snapPoints[snapPoints.length - 1];\n        if (isLastSnapPoint && dismissible) {\n            closeDrawer();\n            return;\n        }\n        const currentSnapIndex = snapPoints.findIndex((point)=>point === activeSnapPoint);\n        if (currentSnapIndex === -1) return; // activeSnapPoint not found in snapPoints\n        const nextSnapPoint = snapPoints[currentSnapIndex + 1];\n        setActiveSnapPoint(nextSnapPoint);\n    }\n    function handleStartInteraction() {\n        closeTimeoutIdRef.current = window.setTimeout(()=>{\n            // Cancel click interaction on a long press\n            shouldCancelInteractionRef.current = true;\n        }, LONG_HANDLE_PRESS_TIMEOUT);\n    }\n    function handleCancelInteraction() {\n        window.clearTimeout(closeTimeoutIdRef.current);\n        shouldCancelInteractionRef.current = false;\n    }\n    return /*#__PURE__*/ React__default.createElement(\"div\", {\n        onClick: handleStartCycle,\n        onPointerCancel: handleCancelInteraction,\n        onPointerDown: (e)=>{\n            if (handleOnly) onPress(e);\n            handleStartInteraction();\n        },\n        onPointerMove: (e)=>{\n            if (handleOnly) onDrag(e);\n        },\n        // onPointerUp is already handled by the content component\n        ref: ref,\n        \"data-vaul-drawer-visible\": isOpen ? 'true' : 'false',\n        \"data-vaul-handle\": \"\",\n        \"aria-hidden\": \"true\",\n        ...rest\n    }, /*#__PURE__*/ React__default.createElement(\"span\", {\n        \"data-vaul-handle-hitarea\": \"\",\n        \"aria-hidden\": \"true\"\n    }, children));\n});\nHandle.displayName = 'Drawer.Handle';\nfunction NestedRoot({ onDrag, onOpenChange, ...rest }) {\n    const { onNestedDrag, onNestedOpenChange, onNestedRelease } = useDrawerContext();\n    if (!onNestedDrag) {\n        throw new Error('Drawer.NestedRoot must be placed in another drawer');\n    }\n    return /*#__PURE__*/ React__default.createElement(Root, {\n        nested: true,\n        onClose: ()=>{\n            onNestedOpenChange(false);\n        },\n        onDrag: (e, p)=>{\n            onNestedDrag(e, p);\n            onDrag == null ? void 0 : onDrag(e, p);\n        },\n        onOpenChange: (o)=>{\n            if (o) {\n                onNestedOpenChange(o);\n            }\n        },\n        onRelease: onNestedRelease,\n        ...rest\n    });\n}\nfunction Portal(props) {\n    const context = useDrawerContext();\n    const { container = context.container, ...portalProps } = props;\n    return /*#__PURE__*/ React__default.createElement(DialogPrimitive.Portal, {\n        container: container,\n        ...portalProps\n    });\n}\nconst Drawer = {\n    Root,\n    NestedRoot,\n    Content,\n    Overlay,\n    Trigger: DialogPrimitive.Trigger,\n    Portal,\n    Handle,\n    Close: DialogPrimitive.Close,\n    Title: DialogPrimitive.Title,\n    Description: DialogPrimitive.Description\n};\n\nexport { Content, Drawer, Handle, NestedRoot, Overlay, Portal, Root };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAWA,YAAuB;AACvB,mBAAoE;AAXpE,SAAS,YAAY,MAAM;AACzB,MAAI,CAAC,QAAQ,OAAO,YAAY,YAAa;AAC7C,MAAI,OAAO,SAAS,QAAQ,SAAS,qBAAqB,MAAM,EAAE,CAAC;AACnE,MAAI,QAAQ,SAAS,cAAc,OAAO;AAC1C,QAAM,OAAO;AACb,OAAK,YAAY,KAAK;AACrB,QAAM,aAAc,MAAM,WAAW,UAAU,OAAQ,MAAM,YAAY,SAAS,eAAe,IAAI,CAAC;AACzG;AAMA,IAAM,gBAAgB,aAAAA,QAAe,cAAc;AAAA,EAC/C,WAAW;AAAA,IACP,SAAS;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACR,SAAS;AAAA,EACb;AAAA,EACA,SAAS,MAAI;AAAA,EAAC;AAAA,EACd,WAAW,MAAI;AAAA,EAAC;AAAA,EAChB,QAAQ,MAAI;AAAA,EAAC;AAAA,EACb,cAAc,MAAI;AAAA,EAAC;AAAA,EACnB,oBAAoB,MAAI;AAAA,EAAC;AAAA,EACzB,iBAAiB,MAAI;AAAA,EAAC;AAAA,EACtB,UAAU;AAAA,EACV,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,gBAAgB;AAAA,IACZ,SAAS;AAAA,EACb;AAAA,EACA,kBAAkB;AAAA,EAClB,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,cAAc,MAAI;AAAA,EAAC;AAAA,EACnB,oBAAoB,MAAI;AAAA,EAAC;AAAA,EACzB,aAAa,MAAI;AAAA,EAAC;AAAA,EAClB,WAAW;AAAA,EACX,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,cAAc;AAAA,EACd,WAAW;AAAA,EACX,WAAW;AACf,CAAC;AACD,IAAM,mBAAmB,MAAI;AACzB,QAAM,UAAU,aAAAA,QAAe,WAAW,aAAa;AACvD,MAAI,CAAC,SAAS;AACV,UAAM,IAAI,MAAM,oDAAoD;AAAA,EACxE;AACA,SAAO;AACX;AAEA,YAAY,smJAAsmJ;AAGlnJ,IAAM,kBAAkB;AACxB,IAAM,4BAA4B,OAAO,WAAW,cAAc,+BAAkB;AACpF,SAAS,WAAW,WAAW;AAC3B,SAAO,IAAI,SAAO;AACd,aAAS,YAAY,WAAU;AAC3B,UAAI,OAAO,aAAa,YAAY;AAChC,iBAAS,GAAG,IAAI;AAAA,MACpB;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,SAAS,QAAQ;AACb,SAAO,aAAa,MAAM;AAC9B;AACA,SAAS,WAAW;AAChB,SAAO,aAAa,SAAS;AACjC;AACA,SAAS,WAAW;AAChB,SAAO,iCAAiC,KAAK,UAAU,SAAS;AACpE;AACA,SAAS,SAAS;AACd,SAAO,aAAa,OAAO;AAAA,EAC3B,MAAM,KAAK,UAAU,iBAAiB;AAC1C;AACA,SAAS,QAAQ;AACb,SAAO,SAAS,KAAK,OAAO;AAChC;AACA,SAAS,aAAa,IAAI;AACtB,SAAO,OAAO,WAAW,eAAe,OAAO,aAAa,OAAO,GAAG,KAAK,OAAO,UAAU,QAAQ,IAAI;AAC5G;AAEA,IAAM,iBAAiB,OAAO,aAAa,eAAe,OAAO;AACjE,SAAS,aAAa,MAAM;AACxB,MAAI,QAAQ,OAAO,iBAAiB,IAAI;AACxC,SAAO,gBAAgB,KAAK,MAAM,WAAW,MAAM,YAAY,MAAM,SAAS;AAClF;AACA,SAAS,gBAAgB,MAAM;AAC3B,MAAI,aAAa,IAAI,GAAG;AACpB,WAAO,KAAK;AAAA,EAChB;AACA,SAAM,QAAQ,CAAC,aAAa,IAAI,GAAE;AAC9B,WAAO,KAAK;AAAA,EAChB;AACA,SAAO,QAAQ,SAAS,oBAAoB,SAAS;AACzD;AAEA,IAAM,oBAAoB,oBAAI,IAAI;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ,CAAC;AAED,IAAI,qBAAqB;AACzB,IAAI;AAKA,SAAS,iBAAiB,UAAU,CAAC,GAAG;AACxC,MAAI,EAAE,WAAW,IAAI;AACrB,4BAA0B,MAAI;AAC1B,QAAI,YAAY;AACZ;AAAA,IACJ;AACA;AACA,QAAI,uBAAuB,GAAG;AAC1B,UAAI,MAAM,GAAG;AACT,kBAAU,0BAA0B;AAAA,MACxC;AAAA,IACJ;AACA,WAAO,MAAI;AACP;AACA,UAAI,uBAAuB,GAAG;AAC1B,mBAAW,OAAO,SAAS,QAAQ;AAAA,MACvC;AAAA,IACJ;AAAA,EACJ,GAAG;AAAA,IACC;AAAA,EACJ,CAAC;AACL;AA2BA,SAAS,4BAA4B;AACjC,MAAI;AACJ,MAAI,QAAQ;AACZ,MAAI,eAAe,CAAC,MAAI;AAEpB,iBAAa,gBAAgB,EAAE,MAAM;AACrC,QAAI,eAAe,SAAS,mBAAmB,eAAe,SAAS,MAAM;AACzE;AAAA,IACJ;AACA,YAAQ,EAAE,eAAe,CAAC,EAAE;AAAA,EAChC;AACA,MAAI,cAAc,CAAC,MAAI;AAEnB,QAAI,CAAC,cAAc,eAAe,SAAS,mBAAmB,eAAe,SAAS,MAAM;AACxF,QAAE,eAAe;AACjB;AAAA,IACJ;AAKA,QAAI,IAAI,EAAE,eAAe,CAAC,EAAE;AAC5B,QAAI,YAAY,WAAW;AAC3B,QAAI,SAAS,WAAW,eAAe,WAAW;AAClD,QAAI,WAAW,GAAG;AACd;AAAA,IACJ;AACA,QAAI,aAAa,KAAK,IAAI,SAAS,aAAa,UAAU,IAAI,OAAO;AACjE,QAAE,eAAe;AAAA,IACrB;AACA,YAAQ;AAAA,EACZ;AACA,MAAI,aAAa,CAAC,MAAI;AAClB,QAAI,SAAS,EAAE;AAEf,QAAI,QAAQ,MAAM,KAAK,WAAW,SAAS,eAAe;AACtD,QAAE,eAAe;AAIjB,aAAO,MAAM,YAAY;AACzB,aAAO,MAAM;AACb,4BAAsB,MAAI;AACtB,eAAO,MAAM,YAAY;AAAA,MAC7B,CAAC;AAAA,IACL;AAAA,EACJ;AACA,MAAI,UAAU,CAAC,MAAI;AACf,QAAI,SAAS,EAAE;AACf,QAAI,QAAQ,MAAM,GAAG;AAKjB,aAAO,MAAM,YAAY;AACzB,4BAAsB,MAAI;AACtB,eAAO,MAAM,YAAY;AAGzB,YAAI,gBAAgB;AAChB,cAAI,eAAe,SAAS,OAAO,aAAa;AAG5C,kCAAsB,MAAI;AACtB,6BAAe,MAAM;AAAA,YACzB,CAAC;AAAA,UACL,OAAO;AAGH,2BAAe,iBAAiB,UAAU,MAAI,eAAe,MAAM,GAAG;AAAA,cAClE,MAAM;AAAA,YACV,CAAC;AAAA,UACL;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AACA,MAAI,iBAAiB,MAAI;AAGrB,WAAO,SAAS,GAAG,CAAC;AAAA,EACxB;AAIA,MAAI,UAAU,OAAO;AACrB,MAAI,UAAU,OAAO;AACrB,MAAI,gBAAgB,QAAQ,SAAS,SAAS,iBAAiB,gBAAgB,GAAG,OAAO,aAAa,SAAS,gBAAgB,WAAW,IAAI,CAAC;AAE/I,SAAO,SAAS,GAAG,CAAC;AACpB,MAAI,eAAe,QAAQ,SAAS,UAAU,cAAc,cAAc;AAAA,IACtE,SAAS;AAAA,IACT,SAAS;AAAA,EACb,CAAC,GAAG,SAAS,UAAU,aAAa,aAAa;AAAA,IAC7C,SAAS;AAAA,IACT,SAAS;AAAA,EACb,CAAC,GAAG,SAAS,UAAU,YAAY,YAAY;AAAA,IAC3C,SAAS;AAAA,IACT,SAAS;AAAA,EACb,CAAC,GAAG,SAAS,UAAU,SAAS,SAAS,IAAI,GAAG,SAAS,QAAQ,UAAU,cAAc,CAAC;AAC1F,SAAO,MAAI;AAEP,kBAAc;AACd,iBAAa;AACb,WAAO,SAAS,SAAS,OAAO;AAAA,EACpC;AACJ;AAEA,SAAS,SAAS,SAAS,OAAO,OAAO;AACrC,MAAI,MAAM,QAAQ,MAAM,KAAK;AAC7B,UAAQ,MAAM,KAAK,IAAI;AACvB,SAAO,MAAI;AACP,YAAQ,MAAM,KAAK,IAAI;AAAA,EAC3B;AACJ;AAEA,SAAS,SAAS,QAAQ,OAAO,SAAS,SAAS;AAE/C,SAAO,iBAAiB,OAAO,SAAS,OAAO;AAC/C,SAAO,MAAI;AAEP,WAAO,oBAAoB,OAAO,SAAS,OAAO;AAAA,EACtD;AACJ;AACA,SAAS,eAAe,QAAQ;AAC5B,MAAI,OAAO,SAAS,oBAAoB,SAAS;AACjD,SAAM,UAAU,WAAW,MAAK;AAE5B,QAAI,aAAa,gBAAgB,MAAM;AACvC,QAAI,eAAe,SAAS,mBAAmB,eAAe,SAAS,QAAQ,eAAe,QAAQ;AAClG,UAAI,gBAAgB,WAAW,sBAAsB,EAAE;AACvD,UAAI,YAAY,OAAO,sBAAsB,EAAE;AAC/C,UAAI,eAAe,OAAO,sBAAsB,EAAE;AAElD,YAAM,iBAAiB,WAAW,sBAAsB,EAAE,SAAS;AACnE,UAAI,eAAe,gBAAgB;AAC/B,mBAAW,aAAa,YAAY;AAAA,MACxC;AAAA,IACJ;AAEA,aAAS,WAAW;AAAA,EACxB;AACJ;AACA,SAAS,QAAQ,QAAQ;AACrB,SAAO,kBAAkB,oBAAoB,CAAC,kBAAkB,IAAI,OAAO,IAAI,KAAK,kBAAkB,uBAAuB,kBAAkB,eAAe,OAAO;AACzK;AAMI,SAAS,OAAO,KAAK,OAAO;AAC5B,MAAI,OAAO,QAAQ,YAAY;AAC3B,QAAI,KAAK;AAAA,EACb,WAAW,QAAQ,QAAQ,QAAQ,QAAW;AAC1C,QAAI,UAAU;AAAA,EAClB;AACJ;AAII,SAAS,eAAe,MAAM;AAC9B,SAAO,CAAC,SAAO,KAAK,QAAQ,CAAC,QAAM,OAAO,KAAK,IAAI,CAAC;AACxD;AAII,SAAS,mBAAmB,MAAM;AAElC,SAAa,kBAAY,YAAY,GAAG,IAAI,GAAG,IAAI;AACvD;AAEA,IAAM,QAAQ,oBAAI,QAAQ;AAC1B,SAAS,IAAI,IAAI,QAAQ,cAAc,OAAO;AAC1C,MAAI,CAAC,MAAM,EAAE,cAAc,aAAc;AACzC,MAAI,iBAAiB,CAAC;AACtB,SAAO,QAAQ,MAAM,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAI;AAC3C,QAAI,IAAI,WAAW,IAAI,GAAG;AACtB,SAAG,MAAM,YAAY,KAAK,KAAK;AAC/B;AAAA,IACJ;AACA,mBAAe,GAAG,IAAI,GAAG,MAAM,GAAG;AAClC,OAAG,MAAM,GAAG,IAAI;AAAA,EACpB,CAAC;AACD,MAAI,YAAa;AACjB,QAAM,IAAI,IAAI,cAAc;AAChC;AACA,SAAS,MAAM,IAAI,MAAM;AACrB,MAAI,CAAC,MAAM,EAAE,cAAc,aAAc;AACzC,MAAI,iBAAiB,MAAM,IAAI,EAAE;AACjC,MAAI,CAAC,gBAAgB;AACjB;AAAA,EACJ;AACA;AACI,OAAG,MAAM,IAAI,IAAI,eAAe,IAAI;AAAA,EACxC;AACJ;AACA,IAAM,aAAa,CAAC,cAAY;AAC5B,UAAO,WAAU;AAAA,IACb,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX;AACI,aAAO;AAAA,EACf;AACJ;AACA,SAAS,aAAa,SAAS,WAAW;AACtC,MAAI,CAAC,SAAS;AACV,WAAO;AAAA,EACX;AACA,QAAM,QAAQ,OAAO,iBAAiB,OAAO;AAC7C,QAAM;AAAA;AAAA,IACN,MAAM,aAAa,MAAM,mBAAmB,MAAM;AAAA;AAClD,MAAI,MAAM,UAAU,MAAM,oBAAoB;AAC9C,MAAI,KAAK;AAEL,WAAO,WAAW,IAAI,CAAC,EAAE,MAAM,IAAI,EAAE,WAAW,SAAS,IAAI,KAAK,EAAE,CAAC;AAAA,EACzE;AAEA,QAAM,UAAU,MAAM,kBAAkB;AACxC,SAAO,MAAM,WAAW,IAAI,CAAC,EAAE,MAAM,IAAI,EAAE,WAAW,SAAS,IAAI,IAAI,CAAC,CAAC,IAAI;AACjF;AACA,SAAS,YAAY,GAAG;AACpB,SAAO,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI;AAClC;AACA,SAAS,YAAY,SAAS,OAAO;AACjC,MAAI,CAAC,QAAS,QAAO,MAAI;AAAA,EAAC;AAC1B,QAAM,YAAY,QAAQ,MAAM;AAChC,SAAO,OAAO,QAAQ,OAAO,KAAK;AAClC,SAAO,MAAI;AACP,YAAQ,MAAM,UAAU;AAAA,EAC5B;AACJ;AAGI,SAAS,SAAS,KAAK;AACvB,SAAO,IAAI,SAAO;AACd,eAAW,MAAM,KAAI;AACjB,UAAI,OAAO,OAAO,YAAY;AAE1B,WAAG,GAAG,IAAI;AAAA,MACd;AAAA,IACJ;AAAA,EACJ;AACJ;AAEA,IAAM,cAAc;AAAA,EAChB,UAAU;AAAA,EACV,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,IAAM,qBAAqB;AAC3B,IAAM,kBAAkB;AACxB,IAAM,sBAAsB;AAC5B,IAAM,gBAAgB;AACtB,IAAM,sBAAsB;AAC5B,IAAM,oBAAoB;AAC1B,IAAM,aAAa;AAGnB,SAAS,eAAe,UAAU;AAC9B,QAAM,cAAc,aAAAA,QAAe,OAAO,QAAQ;AAClD,eAAAA,QAAe,UAAU,MAAI;AACzB,gBAAY,UAAU;AAAA,EAC1B,CAAC;AAED,SAAO,aAAAA,QAAe,QAAQ,MAAI,IAAI,SAAO,YAAY,WAAW,OAAO,SAAS,YAAY,QAAQ,KAAK,aAAa,GAAG,IAAI,GAAG,CAAC,CAAC;AAC1I;AACA,SAAS,qBAAqB,EAAE,aAAa,SAAS,GAAG;AACrD,QAAM,oBAAoB,aAAAA,QAAe,SAAS,WAAW;AAC7D,QAAM,CAAC,KAAK,IAAI;AAChB,QAAM,eAAe,aAAAA,QAAe,OAAO,KAAK;AAChD,QAAM,eAAe,eAAe,QAAQ;AAC5C,eAAAA,QAAe,UAAU,MAAI;AACzB,QAAI,aAAa,YAAY,OAAO;AAChC,mBAAa,KAAK;AAClB,mBAAa,UAAU;AAAA,IAC3B;AAAA,EACJ,GAAG;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AACA,SAAS,qBAAqB,EAAE,MAAM,aAAa,WAAW,MAAI;AAAC,EAAE,GAAG;AACpE,QAAM,CAAC,kBAAkB,mBAAmB,IAAI,qBAAqB;AAAA,IACjE;AAAA,IACA;AAAA,EACJ,CAAC;AACD,QAAM,eAAe,SAAS;AAC9B,QAAM,QAAQ,eAAe,OAAO;AACpC,QAAM,eAAe,eAAe,QAAQ;AAC5C,QAAM,WAAW,aAAAA,QAAe,YAAY,CAAC,cAAY;AACrD,QAAI,cAAc;AACd,YAAM,SAAS;AACf,YAAMC,SAAQ,OAAO,cAAc,aAAa,OAAO,IAAI,IAAI;AAC/D,UAAIA,WAAU,KAAM,cAAaA,MAAK;AAAA,IAC1C,OAAO;AACH,0BAAoB,SAAS;AAAA,IACjC;AAAA,EACJ,GAAG;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACD,SAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AACJ;AAEA,SAAS,cAAc,EAAE,qBAAqB,wBAAwB,YAAY,WAAW,YAAY,eAAe,mBAAmB,YAAY,UAAU,WAAW,sBAAsB,GAAG;AACjM,QAAM,CAAC,iBAAiB,kBAAkB,IAAI,qBAAqB;AAAA,IAC/D,MAAM;AAAA,IACN,aAAa,cAAc,OAAO,SAAS,WAAW,CAAC;AAAA,IACvD,UAAU;AAAA,EACd,CAAC;AACD,QAAM,CAAC,kBAAkB,mBAAmB,IAAI,aAAAD,QAAe,SAAS,OAAO,WAAW,cAAc;AAAA,IACpG,YAAY,OAAO;AAAA,IACnB,aAAa,OAAO;AAAA,EACxB,IAAI,MAAS;AACb,eAAAA,QAAe,UAAU,MAAI;AACzB,aAAS,WAAW;AAChB,0BAAoB;AAAA,QAChB,YAAY,OAAO;AAAA,QACnB,aAAa,OAAO;AAAA,MACxB,CAAC;AAAA,IACL;AACA,WAAO,iBAAiB,UAAU,QAAQ;AAC1C,WAAO,MAAI,OAAO,oBAAoB,UAAU,QAAQ;AAAA,EAC5D,GAAG,CAAC,CAAC;AACL,QAAM,kBAAkB,aAAAA,QAAe,QAAQ,MAAI,qBAAqB,cAAc,OAAO,SAAS,WAAW,WAAW,SAAS,CAAC,MAAM,MAAM;AAAA,IAC9I;AAAA,IACA;AAAA,EACJ,CAAC;AACD,QAAM,uBAAuB,aAAAA,QAAe,QAAQ,MAAI,cAAc,OAAO,SAAS,WAAW,UAAU,CAAC,cAAY,cAAc,eAAe,GAAG;AAAA,IACpJ;AAAA,IACA;AAAA,EACJ,CAAC;AACD,QAAM,aAAa,cAAc,WAAW,SAAS,MAAM,iBAAiB,kBAAkB,MAAM,CAAC,OAAO,MAAM,aAAa,KAAK,WAAW,aAAa,MAAM,mBAAmB,CAAC;AACtL,QAAM,mBAAmB,aAAAA,QAAe,QAAQ,MAAI;AAChD,UAAM,gBAAgB,YAAY;AAAA,MAC9B,OAAO,UAAU,sBAAsB,EAAE;AAAA,MACzC,QAAQ,UAAU,sBAAsB,EAAE;AAAA,IAC9C,IAAI,OAAO,WAAW,cAAc;AAAA,MAChC,OAAO,OAAO;AAAA,MACd,QAAQ,OAAO;AAAA,IACnB,IAAI;AAAA,MACA,OAAO;AAAA,MACP,QAAQ;AAAA,IACZ;AACA,QAAI;AACJ,YAAQ,kBAAkB,cAAc,OAAO,SAAS,WAAW,IAAI,CAAC,cAAY;AAChF,YAAM,OAAO,OAAO,cAAc;AAClC,UAAI,oBAAoB;AACxB,UAAI,MAAM;AACN,4BAAoB,SAAS,WAAW,EAAE;AAAA,MAC9C;AACA,UAAI,WAAW,SAAS,GAAG;AACvB,cAAM,SAAS,OAAO,oBAAoB,mBAAmB,YAAY,cAAc,SAAS;AAChG,YAAI,kBAAkB;AAClB,iBAAO,cAAc,WAAW,cAAc,SAAS,SAAS,CAAC,cAAc,SAAS;AAAA,QAC5F;AACA,eAAO;AAAA,MACX;AACA,YAAM,QAAQ,OAAO,oBAAoB,mBAAmB,YAAY,cAAc,QAAQ;AAC9F,UAAI,kBAAkB;AAClB,eAAO,cAAc,UAAU,cAAc,QAAQ,QAAQ,CAAC,cAAc,QAAQ;AAAA,MACxF;AACA,aAAO;AAAA,IACX,CAAC,MAAM,OAAO,kBAAkB,CAAC;AAAA,EACrC,GAAG;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACD,QAAM,wBAAwB,aAAAA,QAAe,QAAQ,MAAI,yBAAyB,OAAO,oBAAoB,OAAO,SAAS,iBAAiB,oBAAoB,IAAI,MAAM;AAAA,IACxK;AAAA,IACA;AAAA,EACJ,CAAC;AACD,QAAM,cAAc,aAAAA,QAAe,YAAY,CAAC,cAAY;AACxD,QAAI;AACJ,UAAM,qBAAqB,8BAA8B,oBAAoB,OAAO,SAAS,iBAAiB,UAAU,CAAC,iBAAe,iBAAiB,SAAS,MAAM,OAAO,8BAA8B;AAC7M,sBAAkB,iBAAiB;AACnC,QAAI,UAAU,SAAS;AAAA,MACnB,YAAY,aAAa,YAAY,QAAQ,kBAAkB,YAAY,KAAK,KAAK,GAAG,CAAC;AAAA,MACzF,WAAW,WAAW,SAAS,IAAI,kBAAkB,SAAS,WAAW,eAAe,SAAS;AAAA,IACrG,CAAC;AACD,QAAI,oBAAoB,sBAAsB,iBAAiB,SAAS,KAAK,sBAAsB,iBAAiB,oBAAoB,eAAe;AACnJ,UAAI,WAAW,SAAS;AAAA,QACpB,YAAY,WAAW,YAAY,QAAQ,kBAAkB,YAAY,KAAK,KAAK,GAAG,CAAC;AAAA,QACvF,SAAS;AAAA,MACb,CAAC;AAAA,IACL,OAAO;AACH,UAAI,WAAW,SAAS;AAAA,QACpB,YAAY,WAAW,YAAY,QAAQ,kBAAkB,YAAY,KAAK,KAAK,GAAG,CAAC;AAAA,QACvF,SAAS;AAAA,MACb,CAAC;AAAA,IACL;AACA,uBAAmB,cAAc,OAAO,SAAS,WAAW,KAAK,IAAI,mBAAmB,CAAC,CAAC,CAAC;AAAA,EAC/F,GAAG;AAAA,IACC,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACD,eAAAA,QAAe,UAAU,MAAI;AACzB,QAAI,mBAAmB,qBAAqB;AACxC,UAAI;AACJ,YAAM,YAAY,wBAAwB,cAAc,OAAO,SAAS,WAAW,UAAU,CAAC,cAAY,cAAc,uBAAuB,cAAc,eAAe,MAAM,OAAO,wBAAwB;AACjN,UAAI,oBAAoB,aAAa,MAAM,OAAO,iBAAiB,QAAQ,MAAM,UAAU;AACvF,oBAAY,iBAAiB,QAAQ,CAAC;AAAA,MAC1C;AAAA,IACJ;AAAA,EACJ,GAAG;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACD,WAAS,UAAU,EAAE,iBAAiB,aAAa,UAAU,YAAY,GAAG;AACxE,QAAI,kBAAkB,OAAW;AACjC,UAAM,kBAAkB,cAAc,YAAY,cAAc,WAAW,yBAAyB,OAAO,wBAAwB,KAAK,mBAAmB,yBAAyB,OAAO,wBAAwB,KAAK;AACxN,UAAM,qBAAqB,yBAAyB,gBAAgB;AACpE,UAAM,UAAU,yBAAyB;AACzC,UAAM,eAAe,kBAAkB;AACvC,QAAI,oBAAoB;AACpB,UAAI,WAAW,SAAS;AAAA,QACpB,YAAY,WAAW,YAAY,QAAQ,kBAAkB,YAAY,KAAK,KAAK,GAAG,CAAC;AAAA,MAC3F,CAAC;AAAA,IACL;AACA,QAAI,CAAC,yBAAyB,WAAW,KAAK,CAAC,cAAc;AACzD,UAAI,YAAa,aAAY;AAAA,UACxB,aAAY,iBAAiB,CAAC,CAAC;AACpC;AAAA,IACJ;AACA,QAAI,CAAC,yBAAyB,WAAW,KAAK,gBAAgB,oBAAoB,YAAY;AAC1F,kBAAY,iBAAiB,WAAW,SAAS,CAAC,CAAC;AACnD;AAAA,IACJ;AAEA,UAAM,mBAAmB,oBAAoB,OAAO,SAAS,iBAAiB,OAAO,CAAC,MAAM,SAAO;AAC/F,UAAI,OAAO,SAAS,YAAY,OAAO,SAAS,SAAU,QAAO;AACjE,aAAO,KAAK,IAAI,OAAO,eAAe,IAAI,KAAK,IAAI,OAAO,eAAe,IAAI,OAAO;AAAA,IACxF,CAAC;AACD,UAAM,MAAM,WAAW,SAAS,IAAI,OAAO,cAAc,OAAO;AAChE,QAAI,WAAW,sBAAsB,KAAK,IAAI,eAAe,IAAI,MAAM,KAAK;AACxE,YAAM,gBAAgB,eAAe,IAAI;AAEzC,UAAI,gBAAgB,KAAK,iBAAiB;AACtC,oBAAY,iBAAiB,WAAW,SAAS,CAAC,CAAC;AACnD;AAAA,MACJ;AACA,UAAI,WAAW,gBAAgB,KAAK,aAAa;AAC7C,oBAAY;AAAA,MAChB;AACA,UAAI,yBAAyB,KAAM;AACnC,kBAAY,iBAAiB,uBAAuB,aAAa,CAAC;AAClE;AAAA,IACJ;AACA,gBAAY,gBAAgB;AAAA,EAChC;AACA,WAAS,OAAO,EAAE,gBAAgB,GAAG;AACjC,QAAI,0BAA0B,KAAM;AACpC,UAAM,WAAW,cAAc,YAAY,cAAc,UAAU,wBAAwB,kBAAkB,wBAAwB;AAErI,SAAK,cAAc,YAAY,cAAc,YAAY,WAAW,iBAAiB,iBAAiB,SAAS,CAAC,GAAG;AAC/G;AAAA,IACJ;AACA,SAAK,cAAc,SAAS,cAAc,WAAW,WAAW,iBAAiB,iBAAiB,SAAS,CAAC,GAAG;AAC3G;AAAA,IACJ;AACA,QAAI,UAAU,SAAS;AAAA,MACnB,WAAW,WAAW,SAAS,IAAI,kBAAkB,QAAQ,WAAW,eAAe,QAAQ;AAAA,IACnG,CAAC;AAAA,EACL;AACA,WAAS,qBAAqB,oBAAoB,gBAAgB;AAC9D,QAAI,CAAC,cAAc,OAAO,yBAAyB,YAAY,CAAC,oBAAoB,kBAAkB,OAAW,QAAO;AAExH,UAAM,qBAAqB,yBAAyB,gBAAgB;AACpE,UAAM,6BAA6B,wBAAwB;AAC3D,QAAI,8BAA8B,gBAAgB;AAC9C,aAAO;AAAA,IACX;AAEA,QAAI,sBAAsB,CAAC,eAAgB,QAAO;AAClD,QAAI,CAAC,cAAc,CAAC,mBAAoB,QAAO;AAE/C,UAAM,uBAAuB,qBAAqB,uBAAuB,IAAI,uBAAuB;AAEpG,UAAM,oBAAoB,qBAAqB,iBAAiB,oBAAoB,IAAI,iBAAiB,uBAAuB,CAAC,IAAI,iBAAiB,uBAAuB,CAAC,IAAI,iBAAiB,oBAAoB;AACvN,UAAM,oBAAoB,qBAAqB,KAAK,IAAI,iBAAiB;AACzE,QAAI,oBAAoB;AACpB,aAAO,IAAI;AAAA,IACf,OAAO;AACH,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AAEA,IAAM,OAAO,MAAI,MAAI;AAAC;AACtB,SAAS,qBAAqB;AAC1B,QAAM,EAAE,WAAW,QAAQ,uBAAuB,2BAA2B,aAAa,IAAI,iBAAiB;AAC/G,QAAM,eAAe,aAAAA,QAAe,OAAO,IAAI;AAC/C,QAAM,6BAAyB,sBAAQ,MAAI,SAAS,KAAK,MAAM,iBAAiB,CAAC,CAAC;AAClF,WAAS,WAAW;AAChB,YAAQ,OAAO,aAAa,qBAAqB,OAAO;AAAA,EAC5D;AACA,eAAAA,QAAe,UAAU,MAAI;AACzB,QAAI,UAAU,uBAAuB;AACjC,UAAI,aAAa,QAAS,cAAa,aAAa,OAAO;AAC3D,YAAM,UAAU,SAAS,cAAc,4BAA4B,KAAK,SAAS,cAAc,uBAAuB;AACtH,UAAI,CAAC,QAAS;AACd,YAAM,6BAA6B,CAAC,eAAe,YAAY,SAAS,MAAM;AAAA,QAC1E,YAAY;AAAA,MAChB,CAAC,IAAI,MAAM,YAAY,SAAS;AAAA,QAC5B,iBAAiB,WAAW,SAAS,IAAI,QAAQ;AAAA,QACjD,oBAAoB;AAAA,QACpB,oBAAoB,GAAG,YAAY,QAAQ;AAAA,QAC3C,0BAA0B,gBAAgB,YAAY,KAAK,KAAK,GAAG,CAAC;AAAA,MACxE,CAAC,CAAC;AACF,YAAM,uBAAuB,YAAY,SAAS;AAAA,QAC9C,cAAc,GAAG,aAAa;AAAA,QAC9B,UAAU;AAAA,QACV,GAAG,WAAW,SAAS,IAAI;AAAA,UACvB,WAAW,SAAS,SAAS,CAAC;AAAA,QAClC,IAAI;AAAA,UACA,WAAW,SAAS,SAAS,CAAC;AAAA,QAClC;AAAA,MACJ,CAAC;AACD,aAAO,MAAI;AACP,6BAAqB;AACrB,qBAAa,UAAU,OAAO,WAAW,MAAI;AACzC,cAAI,wBAAwB;AACxB,qBAAS,KAAK,MAAM,aAAa;AAAA,UACrC,OAAO;AACH,qBAAS,KAAK,MAAM,eAAe,YAAY;AAAA,UACnD;AAAA,QACJ,GAAG,YAAY,WAAW,GAAI;AAAA,MAClC;AAAA,IACJ;AAAA,EACJ,GAAG;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACL;AAEA,IAAI,uBAAuB;AAQvB,SAAS,iBAAiB,EAAE,QAAQ,OAAO,QAAQ,eAAe,0BAA0B,aAAa,GAAG;AAC5G,QAAM,CAAC,WAAW,YAAY,IAAI,aAAAA,QAAe,SAAS,MAAI,OAAO,WAAW,cAAc,OAAO,SAAS,OAAO,EAAE;AACvH,QAAM,YAAY,aAAAA,QAAe,OAAO,CAAC;AACzC,QAAM,mBAAmB,aAAAA,QAAe,YAAY,MAAI;AAEpD,QAAI,CAAC,SAAS,EAAG;AAEjB,QAAI,yBAAyB,QAAQ,UAAU,CAAC,cAAc;AAC1D,6BAAuB;AAAA,QACnB,UAAU,SAAS,KAAK,MAAM;AAAA,QAC9B,KAAK,SAAS,KAAK,MAAM;AAAA,QACzB,MAAM,SAAS,KAAK,MAAM;AAAA,QAC1B,QAAQ,SAAS,KAAK,MAAM;AAAA,QAC5B,OAAO;AAAA,MACX;AAEA,YAAM,EAAE,SAAS,YAAY,IAAI;AACjC,eAAS,KAAK,MAAM,YAAY,YAAY,SAAS,WAAW;AAChE,aAAO,OAAO,SAAS,KAAK,OAAO;AAAA,QAC/B,KAAK,GAAG,CAAC,UAAU,OAAO;AAAA,QAC1B,MAAM,GAAG,CAAC,OAAO;AAAA,QACjB,OAAO;AAAA,QACP,QAAQ;AAAA,MACZ,CAAC;AACD,aAAO,WAAW,MAAI,OAAO,sBAAsB,MAAI;AAE/C,cAAM,kBAAkB,cAAc,OAAO;AAC7C,YAAI,mBAAmB,UAAU,WAAW,aAAa;AAErD,mBAAS,KAAK,MAAM,MAAM,GAAG,EAAE,UAAU,UAAU,gBAAgB;AAAA,QACvE;AAAA,MACJ,CAAC,GAAG,GAAG;AAAA,IACf;AAAA,EACJ,GAAG;AAAA,IACC;AAAA,EACJ,CAAC;AACD,QAAM,yBAAyB,aAAAA,QAAe,YAAY,MAAI;AAE1D,QAAI,CAAC,SAAS,EAAG;AACjB,QAAI,yBAAyB,QAAQ,CAAC,cAAc;AAEhD,YAAM,IAAI,CAAC,SAAS,SAAS,KAAK,MAAM,KAAK,EAAE;AAC/C,YAAM,IAAI,CAAC,SAAS,SAAS,KAAK,MAAM,MAAM,EAAE;AAEhD,aAAO,OAAO,SAAS,KAAK,OAAO,oBAAoB;AACvD,aAAO,sBAAsB,MAAI;AAC7B,YAAI,4BAA4B,cAAc,OAAO,SAAS,MAAM;AAChE,uBAAa,OAAO,SAAS,IAAI;AACjC;AAAA,QACJ;AACA,eAAO,SAAS,GAAG,CAAC;AAAA,MACxB,CAAC;AACD,6BAAuB;AAAA,IAC3B;AAAA,EACJ,GAAG;AAAA,IACC;AAAA,EACJ,CAAC;AACD,eAAAA,QAAe,UAAU,MAAI;AACzB,aAAS,WAAW;AAChB,gBAAU,UAAU,OAAO;AAAA,IAC/B;AACA,aAAS;AACT,WAAO,iBAAiB,UAAU,QAAQ;AAC1C,WAAO,MAAI;AACP,aAAO,oBAAoB,UAAU,QAAQ;AAAA,IACjD;AAAA,EACJ,GAAG,CAAC,CAAC;AACL,eAAAA,QAAe,UAAU,MAAI;AACzB,QAAI,UAAU,CAAC,cAAe;AAE9B,QAAI,QAAQ;AAER,YAAM,eAAe,OAAO,WAAW,4BAA4B,EAAE;AACrE,OAAC,gBAAgB,iBAAiB;AAClC,UAAI,CAAC,OAAO;AACR,eAAO,WAAW,MAAI;AAClB,iCAAuB;AAAA,QAC3B,GAAG,GAAG;AAAA,MACV;AAAA,IACJ,OAAO;AACH,6BAAuB;AAAA,IAC3B;AAAA,EACJ,GAAG;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACD,SAAO;AAAA,IACH;AAAA,EACJ;AACJ;AAEA,SAASE,MAAK,EAAE,MAAM,UAAU,cAAc,UAAU,QAAQ,YAAY,WAAW,eAAe,YAAY,wBAAwB,OAAO,4BAA4B,MAAM,iBAAiB,iBAAiB,oBAAoB,qBAAqB,cAAc,MAAM,aAAa,OAAO,gBAAgB,cAAc,WAAW,SAAS,GAAG,iBAAiB,qBAAqB,oBAAoB,wBAAwB,OAAO,QAAQ,MAAM,SAAS,QAAQ,cAAc,YAAY,UAAU,cAAc,OAAO,uBAAuB,MAAM,wBAAwB,OAAO,2BAA2B,OAAO,mBAAmB,MAAM,gBAAgB,WAAW,YAAY,MAAM,GAAG;AAC/qB,MAAI,oBAAoB;AACxB,QAAM,CAAC,SAAS,OAAO,SAAS,IAAI,qBAAqB;AAAA,IACrD,aAAa;AAAA,IACb,MAAM;AAAA,IACN,UAAU,CAAC,MAAI;AACX,sBAAgB,OAAO,SAAS,aAAa,CAAC;AAC9C,UAAI,CAAC,KAAK,CAAC,QAAQ;AACf,+BAAuB;AAAA,MAC3B;AACA,iBAAW,MAAI;AACX,0BAAkB,OAAO,SAAS,eAAe,CAAC;AAAA,MACtD,GAAG,YAAY,WAAW,GAAI;AAC9B,UAAI,KAAK,CAAC,OAAO;AACb,YAAI,OAAO,WAAW,aAAa;AAC/B,iBAAO,sBAAsB,MAAI;AAC7B,qBAAS,KAAK,MAAM,gBAAgB;AAAA,UACxC,CAAC;AAAA,QACL;AAAA,MACJ;AACA,UAAI,CAAC,GAAG;AAEJ,iBAAS,KAAK,MAAM,gBAAgB;AAAA,MACxC;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,QAAM,CAAC,eAAe,gBAAgB,IAAI,aAAAF,QAAe,SAAS,KAAK;AACvE,QAAM,CAAC,YAAY,aAAa,IAAI,aAAAA,QAAe,SAAS,KAAK;AACjE,QAAM,CAAC,cAAc,eAAe,IAAI,aAAAA,QAAe,SAAS,KAAK;AACrE,QAAM,aAAa,aAAAA,QAAe,OAAO,IAAI;AAC7C,QAAM,WAAW,aAAAA,QAAe,OAAO,IAAI;AAC3C,QAAM,gBAAgB,aAAAA,QAAe,OAAO,IAAI;AAChD,QAAM,cAAc,aAAAA,QAAe,OAAO,IAAI;AAC9C,QAAM,wBAAwB,aAAAA,QAAe,OAAO,IAAI;AACxD,QAAM,kBAAkB,aAAAA,QAAe,OAAO,KAAK;AACnD,QAAM,wBAAwB,aAAAA,QAAe,OAAO,IAAI;AACxD,QAAM,eAAe,aAAAA,QAAe,OAAO,CAAC;AAC5C,QAAM,iBAAiB,aAAAA,QAAe,OAAO,KAAK;AAClD,QAAM,0BAA0B,aAAAA,QAAe,OAAO,CAAC;AACvD,QAAM,YAAY,aAAAA,QAAe,OAAO,IAAI;AAC5C,QAAM,kBAAkB,aAAAA,QAAe,SAAS,qBAAqB,UAAU,YAAY,OAAO,SAAS,mBAAmB,sBAAsB,EAAE,WAAW,CAAC;AAClK,QAAM,iBAAiB,aAAAA,QAAe,SAAS,sBAAsB,UAAU,YAAY,OAAO,SAAS,oBAAoB,sBAAsB,EAAE,UAAU,CAAC;AAClK,QAAM,sBAAsB,aAAAA,QAAe,OAAO,CAAC;AACnD,QAAM,oBAAoB,aAAAA,QAAe,YAAY,CAACG,0BAAuB;AAEzE,QAAI,cAAcA,0BAAyB,iBAAiB,SAAS,EAAG,UAAS,UAAU,oBAAI,KAAK;AAAA,EACxG,GAAG,CAAC,CAAC;AACL,QAAM,EAAE,iBAAiB,sBAAsB,oBAAoB,WAAW,qBAAqB,kBAAkB,QAAQ,kBAAkB,YAAY,sBAAsB,+BAA+B,IAAI,cAAc;AAAA,IAC9N;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACD,mBAAiB;AAAA,IACb,YAAY,CAAC,UAAU,cAAc,CAAC,SAAS,gBAAgB,CAAC,iBAAiB,CAAC,oBAAoB,CAAC;AAAA,EAC3G,CAAC;AACD,QAAM,EAAE,uBAAuB,IAAI,iBAAiB;AAAA,IAChD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACD,WAAS,WAAW;AAChB,YAAQ,OAAO,aAAa,qBAAqB,OAAO;AAAA,EAC5D;AACA,WAAS,QAAQ,OAAO;AACpB,QAAIC,qBAAoBC;AACxB,QAAI,CAAC,eAAe,CAAC,WAAY;AACjC,QAAI,UAAU,WAAW,CAAC,UAAU,QAAQ,SAAS,MAAM,MAAM,EAAG;AACpE,oBAAgB,YAAYD,sBAAqB,UAAU,YAAY,OAAO,SAASA,oBAAmB,sBAAsB,EAAE,WAAW;AAC7I,mBAAe,YAAYC,uBAAsB,UAAU,YAAY,OAAO,SAASA,qBAAoB,sBAAsB,EAAE,UAAU;AAC7I,kBAAc,IAAI;AAClB,kBAAc,UAAU,oBAAI,KAAK;AAEjC,QAAI,MAAM,GAAG;AACT,aAAO,iBAAiB,YAAY,MAAI,gBAAgB,UAAU,OAAO;AAAA,QACrE,MAAM;AAAA,MACV,CAAC;AAAA,IACL;AAEA,UAAM,OAAO,kBAAkB,MAAM,SAAS;AAC9C,iBAAa,UAAU,WAAW,SAAS,IAAI,MAAM,QAAQ,MAAM;AAAA,EACvE;AACA,WAAS,WAAW,IAAI,uBAAuB;AAC3C,QAAI,sBAAsB;AAC1B,QAAI,UAAU;AACd,UAAM,mBAAmB,uBAAuB,OAAO,aAAa,MAAM,OAAO,SAAS,qBAAqB,SAAS;AACxH,UAAM,cAAc,UAAU,UAAU,aAAa,UAAU,SAAS,SAAS,IAAI;AACrF,UAAM,OAAO,oBAAI,KAAK;AACtB,QAAI,QAAQ,aAAa,mBAAmB,KAAK,QAAQ,QAAQ,qBAAqB,GAAG;AACrF,aAAO;AAAA,IACX;AACA,QAAI,cAAc,WAAW,cAAc,QAAQ;AAC/C,aAAO;AAAA,IACX;AAEA,QAAI,SAAS,WAAW,KAAK,QAAQ,IAAI,SAAS,QAAQ,QAAQ,IAAI,KAAK;AACvE,aAAO;AAAA,IACX;AACA,QAAI,gBAAgB,MAAM;AACtB,UAAI,cAAc,WAAW,cAAc,IAAI,cAAc,GAAG;AAC5D,eAAO;AAAA,MACX;AAAA,IACJ;AAEA,QAAI,mBAAmB,gBAAgB,SAAS,GAAG;AAC/C,aAAO;AAAA,IACX;AAEA,QAAI,KAAK,QAAQ,MAAM,iCAAiC,sBAAsB,YAAY,OAAO,SAAS,+BAA+B,QAAQ,KAAK,qBAAqB,gBAAgB,GAAG;AAC1L,4BAAsB,UAAU;AAChC,aAAO;AAAA,IACX;AACA,QAAI,uBAAuB;AACvB,4BAAsB,UAAU;AAEhC,aAAO;AAAA,IACX;AAEA,WAAM,SAAQ;AAEV,UAAI,QAAQ,eAAe,QAAQ,cAAc;AAC7C,YAAI,QAAQ,cAAc,GAAG;AACzB,gCAAsB,UAAU,oBAAI,KAAK;AAEzC,iBAAO;AAAA,QACX;AACA,YAAI,QAAQ,aAAa,MAAM,MAAM,UAAU;AAC3C,iBAAO;AAAA,QACX;AAAA,MACJ;AAEA,gBAAU,QAAQ;AAAA,IACtB;AAEA,WAAO;AAAA,EACX;AACA,WAAS,OAAO,OAAO;AACnB,QAAI,CAAC,UAAU,SAAS;AACpB;AAAA,IACJ;AAEA,QAAI,YAAY;AACZ,YAAM,sBAAsB,cAAc,YAAY,cAAc,UAAU,IAAI;AAClF,YAAM,mBAAmB,aAAa,WAAW,WAAW,SAAS,IAAI,MAAM,QAAQ,MAAM,UAAU;AACvG,YAAM,wBAAwB,kBAAkB;AAEhD,YAAM,gCAAgC,cAAc,CAAC,eAAe,CAAC;AAErE,UAAI,iCAAiC,yBAAyB,EAAG;AAEjE,YAAM,qBAAqB,KAAK,IAAI,eAAe;AACnD,YAAM,UAAU,SAAS,cAAc,4BAA4B;AACnE,YAAM,kBAAkB,cAAc,YAAY,cAAc,QAAQ,gBAAgB,UAAU,eAAe;AAEjH,UAAI,oBAAoB,qBAAqB;AAC7C,YAAM,6BAA6B,+BAA+B,oBAAoB,qBAAqB;AAC3G,UAAI,+BAA+B,MAAM;AACrC,4BAAoB;AAAA,MACxB;AAEA,UAAI,iCAAiC,qBAAqB,GAAG;AACzD;AAAA,MACJ;AACA,UAAI,CAAC,gBAAgB,WAAW,CAAC,WAAW,MAAM,QAAQ,qBAAqB,EAAG;AAClF,gBAAU,QAAQ,UAAU,IAAI,UAAU;AAE1C,sBAAgB,UAAU;AAC1B,UAAI,UAAU,SAAS;AAAA,QACnB,YAAY;AAAA,MAChB,CAAC;AACD,UAAI,WAAW,SAAS;AAAA,QACpB,YAAY;AAAA,MAChB,CAAC;AACD,UAAI,YAAY;AACZ,yBAAiB;AAAA,UACb;AAAA,QACJ,CAAC;AAAA,MACL;AAEA,UAAI,yBAAyB,CAAC,YAAY;AACtC,cAAM,0BAA0B,YAAY,eAAe;AAC3D,cAAM,iBAAiB,KAAK,IAAI,0BAA0B,IAAI,CAAC,IAAI;AACnE,YAAI,UAAU,SAAS;AAAA,UACnB,WAAW,WAAW,SAAS,IAAI,kBAAkB,cAAc,WAAW,eAAe,cAAc;AAAA,QAC/G,CAAC;AACD;AAAA,MACJ;AACA,YAAM,eAAe,IAAI;AACzB,UAAI,cAAc,iBAAiB,yBAAyB,gBAAgB,GAAG;AAC3E,sBAAc,OAAO,SAAS,WAAW,OAAO,iBAAiB;AACjE,YAAI,WAAW,SAAS;AAAA,UACpB,SAAS,GAAG,YAAY;AAAA,UACxB,YAAY;AAAA,QAChB,GAAG,IAAI;AAAA,MACX;AACA,UAAI,WAAW,WAAW,WAAW,uBAAuB;AAExD,cAAM,aAAa,KAAK,IAAI,SAAS,IAAI,qBAAqB,IAAI,SAAS,IAAI,CAAC;AAChF,cAAM,oBAAoB,IAAI,oBAAoB;AAClD,cAAM,iBAAiB,KAAK,IAAI,GAAG,KAAK,oBAAoB,EAAE;AAC9D,YAAI,SAAS;AAAA,UACT,cAAc,GAAG,iBAAiB;AAAA,UAClC,WAAW,WAAW,SAAS,IAAI,SAAS,UAAU,oBAAoB,cAAc,WAAW,SAAS,UAAU,iBAAiB,cAAc;AAAA,UACrJ,YAAY;AAAA,QAChB,GAAG,IAAI;AAAA,MACX;AACA,UAAI,CAAC,YAAY;AACb,cAAM,iBAAiB,qBAAqB;AAC5C,YAAI,UAAU,SAAS;AAAA,UACnB,WAAW,WAAW,SAAS,IAAI,kBAAkB,cAAc,WAAW,eAAe,cAAc;AAAA,QAC/G,CAAC;AAAA,MACL;AAAA,IACJ;AAAA,EACJ;AACA,eAAAL,QAAe,UAAU,MAAI;AACzB,QAAI;AACJ,aAAS,yBAAyB;AAC9B,UAAI,CAAC,UAAU,WAAW,CAAC,iBAAkB;AAC7C,YAAM,iBAAiB,SAAS;AAChC,UAAI,QAAQ,cAAc,KAAK,eAAe,SAAS;AACnD,YAAIM;AACJ,cAAM,yBAAyBA,0BAAyB,OAAO,mBAAmB,OAAO,SAASA,wBAAuB,WAAW;AACpI,cAAM,cAAc,OAAO;AAE3B,YAAI,kBAAkB,cAAc;AACpC,cAAM,eAAe,UAAU,QAAQ,sBAAsB,EAAE,UAAU;AAEzE,cAAM,eAAe,eAAe,cAAc;AAClD,YAAI,CAAC,oBAAoB,SAAS;AAC9B,8BAAoB,UAAU;AAAA,QAClC;AACA,cAAM,gBAAgB,UAAU,QAAQ,sBAAsB,EAAE;AAEhE,YAAI,KAAK,IAAI,wBAAwB,UAAU,eAAe,IAAI,IAAI;AAClE,yBAAe,UAAU,CAAC,eAAe;AAAA,QAC7C;AACA,YAAI,cAAc,WAAW,SAAS,KAAK,oBAAoB,sBAAsB;AACjF,gBAAM,wBAAwB,iBAAiB,oBAAoB,KAAK;AACxE,6BAAmB;AAAA,QACvB;AACA,gCAAwB,UAAU;AAElC,YAAI,eAAe,wBAAwB,eAAe,SAAS;AAC/D,gBAAM,SAAS,UAAU,QAAQ,sBAAsB,EAAE;AACzD,cAAI,kBAAkB;AACtB,cAAI,SAAS,sBAAsB;AAC/B,8BAAkB,wBAAwB,eAAe,gBAAgB;AAAA,UAC7E;AAEA,cAAI,OAAO;AACP,sBAAU,QAAQ,MAAM,SAAS,GAAG,SAAS,KAAK,IAAI,iBAAiB,CAAC,CAAC;AAAA,UAC7E,OAAO;AACH,sBAAU,QAAQ,MAAM,SAAS,GAAG,KAAK,IAAI,iBAAiB,uBAAuB,aAAa,CAAC;AAAA,UACvG;AAAA,QACJ,OAAO;AACH,oBAAU,QAAQ,MAAM,SAAS,GAAG,oBAAoB,OAAO;AAAA,QACnE;AACA,YAAI,cAAc,WAAW,SAAS,KAAK,CAAC,eAAe,SAAS;AAChE,oBAAU,QAAQ,MAAM,SAAS;AAAA,QACrC,OAAO;AAEH,oBAAU,QAAQ,MAAM,SAAS,GAAG,KAAK,IAAI,iBAAiB,CAAC,CAAC;AAAA,QACpE;AAAA,MACJ;AAAA,IACJ;AACA,KAAC,yBAAyB,OAAO,mBAAmB,OAAO,SAAS,uBAAuB,iBAAiB,UAAU,sBAAsB;AAC5I,WAAO,MAAI;AACP,UAAIA;AACJ,cAAQA,0BAAyB,OAAO,mBAAmB,OAAO,SAASA,wBAAuB,oBAAoB,UAAU,sBAAsB;AAAA,IAC1J;AAAA,EACJ,GAAG;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACD,WAAS,YAAY,YAAY;AAC7B,eAAW;AACX,eAAW,OAAO,SAAS,QAAQ;AACnC,QAAI,CAAC,YAAY;AACb,gBAAU,KAAK;AAAA,IACnB;AACA,eAAW,MAAI;AACX,UAAI,YAAY;AACZ,2BAAmB,WAAW,CAAC,CAAC;AAAA,MACpC;AAAA,IACJ,GAAG,YAAY,WAAW,GAAI;AAAA,EAClC;AACA,WAAS,cAAc;AACnB,QAAI,CAAC,UAAU,QAAS;AACxB,UAAM,UAAU,SAAS,cAAc,4BAA4B;AACnE,UAAM,qBAAqB,aAAa,UAAU,SAAS,SAAS;AACpE,QAAI,UAAU,SAAS;AAAA,MACnB,WAAW;AAAA,MACX,YAAY,aAAa,YAAY,QAAQ,kBAAkB,YAAY,KAAK,KAAK,GAAG,CAAC;AAAA,IAC7F,CAAC;AACD,QAAI,WAAW,SAAS;AAAA,MACpB,YAAY,WAAW,YAAY,QAAQ,kBAAkB,YAAY,KAAK,KAAK,GAAG,CAAC;AAAA,MACvF,SAAS;AAAA,IACb,CAAC;AAED,QAAI,yBAAyB,sBAAsB,qBAAqB,KAAK,QAAQ;AACjF,UAAI,SAAS;AAAA,QACT,cAAc,GAAG,aAAa;AAAA,QAC9B,UAAU;AAAA,QACV,GAAG,WAAW,SAAS,IAAI;AAAA,UACvB,WAAW,SAAS,SAAS,CAAC;AAAA,UAC9B,iBAAiB;AAAA,QACrB,IAAI;AAAA,UACA,WAAW,SAAS,SAAS,CAAC;AAAA,UAC9B,iBAAiB;AAAA,QACrB;AAAA,QACA,oBAAoB;AAAA,QACpB,oBAAoB,GAAG,YAAY,QAAQ;AAAA,QAC3C,0BAA0B,gBAAgB,YAAY,KAAK,KAAK,GAAG,CAAC;AAAA,MACxE,GAAG,IAAI;AAAA,IACX;AAAA,EACJ;AACA,WAAS,aAAa;AAClB,QAAI,CAAC,cAAc,CAAC,UAAU,QAAS;AACvC,cAAU,QAAQ,UAAU,OAAO,UAAU;AAC7C,oBAAgB,UAAU;AAC1B,kBAAc,KAAK;AACnB,gBAAY,UAAU,oBAAI,KAAK;AAAA,EACnC;AACA,WAAS,UAAU,OAAO;AACtB,QAAI,CAAC,cAAc,CAAC,UAAU,QAAS;AACvC,cAAU,QAAQ,UAAU,OAAO,UAAU;AAC7C,oBAAgB,UAAU;AAC1B,kBAAc,KAAK;AACnB,gBAAY,UAAU,oBAAI,KAAK;AAC/B,UAAM,cAAc,aAAa,UAAU,SAAS,SAAS;AAC7D,QAAI,CAAC,WAAW,MAAM,QAAQ,KAAK,KAAK,CAAC,eAAe,OAAO,MAAM,WAAW,EAAG;AACnF,QAAI,cAAc,YAAY,KAAM;AACpC,UAAM,YAAY,YAAY,QAAQ,QAAQ,IAAI,cAAc,QAAQ,QAAQ;AAChF,UAAM,YAAY,aAAa,WAAW,WAAW,SAAS,IAAI,MAAM,QAAQ,MAAM;AACtF,UAAM,WAAW,KAAK,IAAI,SAAS,IAAI;AACvC,QAAI,WAAW,MAAM;AAEjB,sBAAgB,IAAI;AACpB,iBAAW,MAAI;AACX,wBAAgB,KAAK;AAAA,MACzB,GAAG,GAAG;AAAA,IACV;AACA,QAAI,YAAY;AACZ,YAAM,sBAAsB,cAAc,YAAY,cAAc,UAAU,IAAI;AAClF,0BAAoB;AAAA,QAChB,iBAAiB,YAAY;AAAA,QAC7B;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC;AACD,uBAAiB,OAAO,SAAS,cAAc,OAAO,IAAI;AAC1D;AAAA,IACJ;AAEA,QAAI,cAAc,YAAY,cAAc,UAAU,YAAY,IAAI,YAAY,GAAG;AACjF,kBAAY;AACZ,uBAAiB,OAAO,SAAS,cAAc,OAAO,IAAI;AAC1D;AAAA,IACJ;AACA,QAAI,WAAW,oBAAoB;AAC/B,kBAAY;AACZ,uBAAiB,OAAO,SAAS,cAAc,OAAO,KAAK;AAC3D;AAAA,IACJ;AACA,QAAI;AACJ,UAAM,sBAAsB,KAAK,KAAK,kDAAkD,UAAU,QAAQ,sBAAsB,EAAE,WAAW,OAAO,kDAAkD,GAAG,OAAO,WAAW;AAC3N,QAAI;AACJ,UAAM,qBAAqB,KAAK,KAAK,iDAAiD,UAAU,QAAQ,sBAAsB,EAAE,UAAU,OAAO,iDAAiD,GAAG,OAAO,UAAU;AACtN,UAAM,oBAAoB,cAAc,UAAU,cAAc;AAChE,QAAI,KAAK,IAAI,WAAW,MAAM,oBAAoB,qBAAqB,uBAAuB,gBAAgB;AAC1G,kBAAY;AACZ,uBAAiB,OAAO,SAAS,cAAc,OAAO,KAAK;AAC3D;AAAA,IACJ;AACA,qBAAiB,OAAO,SAAS,cAAc,OAAO,IAAI;AAC1D,gBAAY;AAAA,EAChB;AACA,eAAAN,QAAe,UAAU,MAAI;AAEzB,QAAI,QAAQ;AACR,UAAI,SAAS,iBAAiB;AAAA,QAC1B,gBAAgB;AAAA,MACpB,CAAC;AACD,eAAS,UAAU,oBAAI,KAAK;AAAA,IAChC;AACA,WAAO,MAAI;AACP,YAAM,SAAS,iBAAiB,gBAAgB;AAAA,IACpD;AAAA,EACJ,GAAG;AAAA,IACC;AAAA,EACJ,CAAC;AACD,WAAS,mBAAmB,GAAG;AAC3B,UAAM,QAAQ,KAAK,OAAO,aAAa,uBAAuB,OAAO,aAAa;AAClF,UAAM,IAAI,IAAI,CAAC,sBAAsB;AACrC,QAAI,sBAAsB,SAAS;AAC/B,aAAO,aAAa,sBAAsB,OAAO;AAAA,IACrD;AACA,QAAI,UAAU,SAAS;AAAA,MACnB,YAAY,aAAa,YAAY,QAAQ,kBAAkB,YAAY,KAAK,KAAK,GAAG,CAAC;AAAA,MACzF,WAAW,SAAS,KAAK,oBAAoB,CAAC;AAAA,IAClD,CAAC;AACD,QAAI,CAAC,KAAK,UAAU,SAAS;AACzB,4BAAsB,UAAU,WAAW,MAAI;AAC3C,cAAM,iBAAiB,aAAa,UAAU,SAAS,SAAS;AAChE,YAAI,UAAU,SAAS;AAAA,UACnB,YAAY;AAAA,UACZ,WAAW,WAAW,SAAS,IAAI,kBAAkB,cAAc,WAAW,eAAe,cAAc;AAAA,QAC/G,CAAC;AAAA,MACL,GAAG,GAAG;AAAA,IACV;AAAA,EACJ;AACA,WAAS,aAAa,QAAQ,mBAAmB;AAC7C,QAAI,oBAAoB,EAAG;AAC3B,UAAM,gBAAgB,OAAO,aAAa,uBAAuB,OAAO;AACxE,UAAM,WAAW,eAAe,qBAAqB,IAAI;AACzD,UAAM,eAAe,CAAC,sBAAsB,oBAAoB;AAChE,QAAI,UAAU,SAAS;AAAA,MACnB,WAAW,WAAW,SAAS,IAAI,SAAS,QAAQ,oBAAoB,YAAY,WAAW,SAAS,QAAQ,iBAAiB,YAAY;AAAA,MAC7I,YAAY;AAAA,IAChB,CAAC;AAAA,EACL;AACA,WAAS,gBAAgB,QAAQ,GAAG;AAChC,UAAM,MAAM,WAAW,SAAS,IAAI,OAAO,cAAc,OAAO;AAChE,UAAM,QAAQ,KAAK,MAAM,uBAAuB,MAAM;AACtD,UAAM,YAAY,IAAI,CAAC,sBAAsB;AAC7C,QAAI,GAAG;AACH,UAAI,UAAU,SAAS;AAAA,QACnB,YAAY,aAAa,YAAY,QAAQ,kBAAkB,YAAY,KAAK,KAAK,GAAG,CAAC;AAAA,QACzF,WAAW,WAAW,SAAS,IAAI,SAAS,KAAK,oBAAoB,SAAS,WAAW,SAAS,KAAK,iBAAiB,SAAS;AAAA,MACrI,CAAC;AAAA,IACL;AAAA,EACJ;AACA,SAAqB,aAAAA,QAAe,cAA8B,MAAM;AAAA,IACpE;AAAA,IACA,cAAc,CAAC,SAAO;AAClB,UAAI,CAAC,eAAe,CAAC,KAAM;AAC3B,UAAI,MAAM;AACN,yBAAiB,IAAI;AAAA,MACzB,OAAO;AACH,oBAAY,IAAI;AAAA,MACpB;AACA,gBAAU,IAAI;AAAA,IAClB;AAAA,IACA,MAAM;AAAA,EACV,GAAiB,aAAAA,QAAe,cAAc,cAAc,UAAU;AAAA,IAClE,OAAO;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ,GAAG,QAAQ,CAAC;AAChB;AACA,IAAMO,WAAwB,aAAAP,QAAe,WAAW,SAAS,EAAE,GAAG,KAAK,GAAG,KAAK;AAC/E,QAAM,EAAE,YAAY,YAAY,WAAW,YAAY,QAAQ,MAAM,IAAI,iBAAiB;AAC1F,QAAM,cAAc,gBAAgB,KAAK,UAAU;AACnD,QAAM,gBAAgB,cAAc,WAAW,SAAS;AAExD,MAAI,CAAC,OAAO;AAER,QAAI,OAAO,WAAW,aAAa;AAC/B,aAAO,sBAAsB,MAAI;AAC7B,iBAAS,KAAK,MAAM,gBAAgB;AAAA,MACxC,CAAC;AAAA,IACL;AACA,WAAO;AAAA,EACX;AACA,SAAqB,aAAAA,QAAe,cAA8B,SAAS;AAAA,IACvE,WAAW;AAAA,IACX,KAAK;AAAA,IACL,qBAAqB;AAAA,IACrB,yBAAyB,UAAU,gBAAgB,SAAS;AAAA,IAC5D,iCAAiC,UAAU,aAAa,SAAS;AAAA,IACjE,GAAG;AAAA,EACP,CAAC;AACL,CAAC;AACDO,SAAQ,cAAc;AACtB,IAAMC,WAAwB,aAAAR,QAAe,WAAW,SAAS,EAAE,sBAAsB,OAAO,iBAAiB,GAAG,KAAK,GAAG,KAAK;AAC7H,QAAM,EAAE,WAAW,SAAS,WAAW,QAAQ,gBAAgB,kBAAkB,OAAO,QAAQ,WAAW,YAAY,WAAW,YAAY,UAAU,IAAI,iBAAiB;AAE7K,QAAM,CAAC,mBAAmB,oBAAoB,IAAI,aAAAA,QAAe,SAAS,KAAK;AAC/E,QAAM,cAAc,gBAAgB,KAAK,SAAS;AAClD,QAAM,kBAAkB,aAAAA,QAAe,OAAO,IAAI;AAClD,QAAM,2BAA2B,aAAAA,QAAe,OAAO,IAAI;AAC3D,QAAM,uBAAuB,aAAAA,QAAe,OAAO,KAAK;AACxD,QAAM,gBAAgB,cAAc,WAAW,SAAS;AACxD,qBAAmB;AACnB,QAAM,qBAAqB,CAAC,OAAOS,YAAW,YAAY,MAAI;AAC1D,QAAI,qBAAqB,QAAS,QAAO;AACzC,UAAM,SAAS,KAAK,IAAI,MAAM,CAAC;AAC/B,UAAM,SAAS,KAAK,IAAI,MAAM,CAAC;AAC/B,UAAM,WAAW,SAAS;AAC1B,UAAM,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,IACJ,EAAE,SAASA,UAAS,IAAI,IAAI;AAC5B,QAAIA,eAAc,UAAUA,eAAc,SAAS;AAC/C,YAAM,qBAAqB,MAAM,IAAI,UAAU;AAC/C,UAAI,CAAC,sBAAsB,UAAU,KAAK,UAAU,WAAW;AAC3D,eAAO;AAAA,MACX;AAAA,IACJ,OAAO;AACH,YAAM,qBAAqB,MAAM,IAAI,UAAU;AAC/C,UAAI,CAAC,sBAAsB,UAAU,KAAK,UAAU,WAAW;AAC3D,eAAO,CAAC;AAAA,MACZ;AAAA,IACJ;AACA,yBAAqB,UAAU;AAC/B,WAAO;AAAA,EACX;AACA,eAAAT,QAAe,UAAU,MAAI;AACzB,QAAI,eAAe;AACf,aAAO,sBAAsB,MAAI;AAC7B,6BAAqB,IAAI;AAAA,MAC7B,CAAC;AAAA,IACL;AAAA,EACJ,GAAG,CAAC,CAAC;AACL,WAAS,kBAAkB,OAAO;AAC9B,oBAAgB,UAAU;AAC1B,yBAAqB,UAAU;AAC/B,cAAU,KAAK;AAAA,EACnB;AACA,SAAqB,aAAAA,QAAe,cAA8B,SAAS;AAAA,IACvE,8BAA8B;AAAA,IAC9B,oBAAoB;AAAA,IACpB,iCAAiC,oBAAoB,SAAS;AAAA,IAC9D,yBAAyB,UAAU,gBAAgB,SAAS;AAAA,IAC5D,8BAA8B,YAAY,SAAS;AAAA,IACnD,GAAG;AAAA,IACH,KAAK;AAAA,IACL,OAAO,oBAAoB,iBAAiB,SAAS,IAAI;AAAA,MACrD,uBAAuB,GAAG,iBAAiB,CAAC,CAAC;AAAA,MAC7C,GAAG;AAAA,IACP,IAAI;AAAA,IACJ,eAAe,CAAC,UAAQ;AACpB,UAAI,WAAY;AAChB,WAAK,iBAAiB,OAAO,SAAS,KAAK,cAAc,KAAK,MAAM,KAAK;AACzE,sBAAgB,UAAU;AAAA,QACtB,GAAG,MAAM;AAAA,QACT,GAAG,MAAM;AAAA,MACb;AACA,cAAQ,KAAK;AAAA,IACjB;AAAA,IACA,iBAAiB,CAAC,MAAI;AAClB,yBAAmB,OAAO,SAAS,gBAAgB,CAAC;AACpD,UAAI,CAAC,WAAW;AACZ,UAAE,eAAe;AAAA,MACrB;AAAA,IACJ;AAAA,IACA,sBAAsB,CAAC,MAAI;AACvB,8BAAwB,OAAO,SAAS,qBAAqB,CAAC;AAC9D,UAAI,CAAC,SAAS,EAAE,kBAAkB;AAC9B,UAAE,eAAe;AACjB;AAAA,MACJ;AACA,UAAI,eAAe,SAAS;AACxB,uBAAe,UAAU;AAAA,MAC7B;AAAA,IACJ;AAAA,IACA,gBAAgB,CAAC,MAAI;AACjB,UAAI,CAAC,OAAO;AACR,UAAE,eAAe;AACjB;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,eAAe,CAAC,UAAQ;AACpB,+BAAyB,UAAU;AACnC,UAAI,WAAY;AAChB,WAAK,iBAAiB,OAAO,SAAS,KAAK,cAAc,KAAK,MAAM,KAAK;AACzE,UAAI,CAAC,gBAAgB,QAAS;AAC9B,YAAM,YAAY,MAAM,QAAQ,gBAAgB,QAAQ;AACxD,YAAM,YAAY,MAAM,QAAQ,gBAAgB,QAAQ;AACxD,YAAM,sBAAsB,MAAM,gBAAgB,UAAU,KAAK;AACjE,YAAM,QAAQ;AAAA,QACV,GAAG;AAAA,QACH,GAAG;AAAA,MACP;AACA,YAAM,mBAAmB,mBAAmB,OAAO,WAAW,mBAAmB;AACjF,UAAI,iBAAkB,QAAO,KAAK;AAAA,eACzB,KAAK,IAAI,SAAS,IAAI,uBAAuB,KAAK,IAAI,SAAS,IAAI,qBAAqB;AAC7F,wBAAgB,UAAU;AAAA,MAC9B;AAAA,IACJ;AAAA,IACA,aAAa,CAAC,UAAQ;AAClB,WAAK,eAAe,OAAO,SAAS,KAAK,YAAY,KAAK,MAAM,KAAK;AACrE,sBAAgB,UAAU;AAC1B,2BAAqB,UAAU;AAC/B,gBAAU,KAAK;AAAA,IACnB;AAAA,IACA,cAAc,CAAC,UAAQ;AACnB,WAAK,gBAAgB,OAAO,SAAS,KAAK,aAAa,KAAK,MAAM,KAAK;AACvE,wBAAkB,yBAAyB,OAAO;AAAA,IACtD;AAAA,IACA,eAAe,CAAC,UAAQ;AACpB,WAAK,iBAAiB,OAAO,SAAS,KAAK,cAAc,KAAK,MAAM,KAAK;AACzE,wBAAkB,yBAAyB,OAAO;AAAA,IACtD;AAAA,EACJ,CAAC;AACL,CAAC;AACDQ,SAAQ,cAAc;AACtB,IAAM,4BAA4B;AAClC,IAAM,qBAAqB;AAC3B,IAAM,SAAuB,aAAAR,QAAe,WAAW,SAAS,EAAE,eAAe,OAAO,UAAU,GAAG,KAAK,GAAG,KAAK;AAC9G,QAAM,EAAE,aAAa,YAAY,YAAY,iBAAiB,oBAAoB,aAAa,YAAY,QAAQ,SAAS,OAAO,IAAI,iBAAiB;AACxJ,QAAM,oBAAoB,aAAAA,QAAe,OAAO,IAAI;AACpD,QAAM,6BAA6B,aAAAA,QAAe,OAAO,KAAK;AAC9D,WAAS,mBAAmB;AAExB,QAAI,2BAA2B,SAAS;AACpC,8BAAwB;AACxB;AAAA,IACJ;AACA,WAAO,WAAW,MAAI;AAClB,4BAAsB;AAAA,IAC1B,GAAG,kBAAkB;AAAA,EACzB;AACA,WAAS,wBAAwB;AAE7B,QAAI,cAAc,gBAAgB,2BAA2B,SAAS;AAClE,8BAAwB;AACxB;AAAA,IACJ;AAEA,4BAAwB;AACxB,SAAK,CAAC,cAAc,WAAW,WAAW,MAAM,aAAa;AACzD,kBAAY;AACZ;AAAA,IACJ;AACA,UAAM,kBAAkB,oBAAoB,WAAW,WAAW,SAAS,CAAC;AAC5E,QAAI,mBAAmB,aAAa;AAChC,kBAAY;AACZ;AAAA,IACJ;AACA,UAAM,mBAAmB,WAAW,UAAU,CAAC,UAAQ,UAAU,eAAe;AAChF,QAAI,qBAAqB,GAAI;AAC7B,UAAM,gBAAgB,WAAW,mBAAmB,CAAC;AACrD,uBAAmB,aAAa;AAAA,EACpC;AACA,WAAS,yBAAyB;AAC9B,sBAAkB,UAAU,OAAO,WAAW,MAAI;AAE9C,iCAA2B,UAAU;AAAA,IACzC,GAAG,yBAAyB;AAAA,EAChC;AACA,WAAS,0BAA0B;AAC/B,WAAO,aAAa,kBAAkB,OAAO;AAC7C,+BAA2B,UAAU;AAAA,EACzC;AACA,SAAqB,aAAAA,QAAe,cAAc,OAAO;AAAA,IACrD,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,eAAe,CAAC,MAAI;AAChB,UAAI,WAAY,SAAQ,CAAC;AACzB,6BAAuB;AAAA,IAC3B;AAAA,IACA,eAAe,CAAC,MAAI;AAChB,UAAI,WAAY,QAAO,CAAC;AAAA,IAC5B;AAAA;AAAA,IAEA;AAAA,IACA,4BAA4B,SAAS,SAAS;AAAA,IAC9C,oBAAoB;AAAA,IACpB,eAAe;AAAA,IACf,GAAG;AAAA,EACP,GAAiB,aAAAA,QAAe,cAAc,QAAQ;AAAA,IAClD,4BAA4B;AAAA,IAC5B,eAAe;AAAA,EACnB,GAAG,QAAQ,CAAC;AAChB,CAAC;AACD,OAAO,cAAc;AACrB,SAAS,WAAW,EAAE,QAAQ,cAAc,GAAG,KAAK,GAAG;AACnD,QAAM,EAAE,cAAc,oBAAoB,gBAAgB,IAAI,iBAAiB;AAC/E,MAAI,CAAC,cAAc;AACf,UAAM,IAAI,MAAM,oDAAoD;AAAA,EACxE;AACA,SAAqB,aAAAA,QAAe,cAAcE,OAAM;AAAA,IACpD,QAAQ;AAAA,IACR,SAAS,MAAI;AACT,yBAAmB,KAAK;AAAA,IAC5B;AAAA,IACA,QAAQ,CAAC,GAAG,MAAI;AACZ,mBAAa,GAAG,CAAC;AACjB,gBAAU,OAAO,SAAS,OAAO,GAAG,CAAC;AAAA,IACzC;AAAA,IACA,cAAc,CAAC,MAAI;AACf,UAAI,GAAG;AACH,2BAAmB,CAAC;AAAA,MACxB;AAAA,IACJ;AAAA,IACA,WAAW;AAAA,IACX,GAAG;AAAA,EACP,CAAC;AACL;AACA,SAASQ,QAAO,OAAO;AACnB,QAAM,UAAU,iBAAiB;AACjC,QAAM,EAAE,YAAY,QAAQ,WAAW,GAAG,YAAY,IAAI;AAC1D,SAAqB,aAAAV,QAAe,cAA8B,QAAQ;AAAA,IACtE;AAAA,IACA,GAAG;AAAA,EACP,CAAC;AACL;AACA,IAAM,SAAS;AAAA,EACX,MAAAE;AAAA,EACA;AAAA,EACA,SAAAM;AAAA,EACA,SAAAD;AAAA,EACA;AAAA,EACA,QAAAG;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;", "names": ["React__default", "value", "Root", "activeSnapPointIndex", "_drawerRef_current", "_drawerRef_current1", "_window_visualViewport", "Overlay", "Content", "direction", "Portal"]}