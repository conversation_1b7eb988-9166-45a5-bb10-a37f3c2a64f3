import { AvatarGlow } from "@/components/AvatarGlow";
import { TierBadge } from "@/components/TierBadge";
import { GlassCard } from "@/components/GlassCard";
import { ProgressBar } from "@/components/ProgressBar";
import { BottomNav } from "@/components/BottomNav";
import { Flame, Footprints, Shield, History, LogOut, Loader2 } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { Link, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useTracking, useTimeTracking } from "@/hooks/useTracking";

const DEFAULT_AVATAR = "https://randomuser.me/api/portraits/men/1.jpg";

const sampleAchievements = [
  { id: 1, title: "5 Day Streak", icon: <Flame size={36} className="text-electric" /> },
  { id: 2, title: "Daily 5km Run", icon: <Footprints size={36} className="text-electric" /> },
  { id: 3, title: "Guild Champion", icon: <Shield size={36} className="text-electric" /> }
];

function AuthenticatedContent() {
    const { profile, signOut, user } = useAuth();
    const navigate = useNavigate();
    const { trackButtonClick, trackNavigation } = useTracking();

    // Track time spent on home page
    useTimeTracking('Home');

    const { data: stats, isLoading: isLoadingStats } = useQuery({
      queryKey: ['user-leaderboard-stats-home', user?.id],
      queryFn: async () => {
        if (!user) return null;
        const { data, error } = await supabase
          .from('user_leaderboard_stats')
          .select('total_distance')
          .eq('user_id', user.id)
          .single();

        if (error && error.code !== 'PGRST116') {
          console.error('Error fetching user stats for home:', error);
          return null;
        }
        return data;
      },
      enabled: !!user,
    });

    // Fetch user settings for goals
    const { data: userSettings, isLoading: isLoadingSettings } = useQuery({
      queryKey: ['user-settings', user?.id],
      queryFn: async () => {
        if (!user) return null;
        const { data, error } = await supabase
          .from('user_settings')
          .select('daily_goal_km, weekly_goal_km')
          .eq('user_id', user.id)
          .single();

        if (error && error.code !== 'PGRST116') {
          console.error('Error fetching user settings:', error);
          return null;
        }
        return data;
      },
      enabled: !!user,
    });

    // Fetch weekly activities (last 7 days)
    const { data: weeklyActivities, isLoading: isLoadingWeekly } = useQuery({
      queryKey: ['weekly-activities', user?.id],
      queryFn: async () => {
        if (!user) return null;
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

        const { data, error } = await supabase
          .from('user_activities')
          .select('distance_km')
          .eq('user_id', user.id)
          .gte('activity_date', sevenDaysAgo.toISOString().split('T')[0]);

        if (error) {
          console.error('Error fetching weekly activities:', error);
          return null;
        }
        return data;
      },
      enabled: !!user,
    });

    // Fetch today's activities
    const { data: todayActivities, isLoading: isLoadingToday } = useQuery({
      queryKey: ['today-activities', user?.id],
      queryFn: async () => {
        if (!user) return null;
        const today = new Date().toISOString().split('T')[0];

        const { data, error } = await supabase
          .from('user_activities')
          .select('distance_km')
          .eq('user_id', user.id)
          .eq('activity_date', today);

        if (error) {
          console.error('Error fetching today activities:', error);
          return null;
        }
        return data;
      },
      enabled: !!user,
    });

    // Calculate metrics
    const weeklyDistance = weeklyActivities?.reduce((sum, activity) => sum + Number(activity.distance_km), 0) || 0;
    const todayDistance = todayActivities?.reduce((sum, activity) => sum + Number(activity.distance_km), 0) || 0;
    const dailyGoal = userSettings?.daily_goal_km || 6;
    const weeklyGoal = userSettings?.weekly_goal_km || 42;

    const weeklyProgress = weeklyGoal > 0 ? Math.min((weeklyDistance / weeklyGoal) * 100, 100) : 0;
    const dailyProgress = dailyGoal > 0 ? Math.min((todayDistance / dailyGoal) * 100, 100) : 0;

    const handleSignOut = async () => {
        trackButtonClick('sign_out', { location: 'home_page' });
        await signOut();
        trackNavigation('home', 'auth');
        navigate('/auth');
    }

    const statCards = [
      { label: "Total Distance", value: isLoadingStats ? '...' : `${stats?.total_distance || 0} km`, color: "from-electric to-purple" },
      { label: "Current Streak", value: "8 days", color: "from-purple to-electric" },
      { label: "Guild Rank", value: "#7", color: "from-electric to-purple" },
    ];

    return (
        <div className="flex-1 flex flex-col">
            {/* Avatar + Tier Badge */}
            <div className="flex items-center justify-between px-3">
                <Link to="/profile" className="flex items-center gap-3 group">
                    <AvatarGlow src={profile?.avatar_url || DEFAULT_AVATAR} size={56} className="animate-glow" />
                    <div>
                        <div className="flex items-center gap-1">
                        <span className="font-bold text-lg group-hover:text-electric transition-colors">{profile?.username || 'SkyRunner'}</span>
                        <TierBadge tier="B" />
                        </div>
                        <span className="text-xs text-white/60">Level 17</span>
                    </div>
                </Link>
                <div className="flex items-center gap-2">
                    <button onClick={handleSignOut} className="glass-card p-2 rounded-full shadow-glow hover:bg-glass/80 border border-white/10">
                        <LogOut size={22} className="text-electric" />
                    </button>
                </div>
            </div>

            {/* Weekly Progress */}
            <GlassCard className="mt-6 mx-4 px-5 py-4">
                <div className="flex justify-between items-center mb-2">
                <span className="font-bold text-white/80">Weekly Distance</span>
                <span className="text-xs text-electric font-semibold">
                  {isLoadingWeekly || isLoadingSettings ? '...' : `${weeklyDistance.toFixed(1)} / ${weeklyGoal} km`}
                </span>
                </div>
                <ProgressBar percent={isLoadingWeekly || isLoadingSettings ? 0 : weeklyProgress} />
                <div className="flex justify-between text-2xs text-white/60 mt-1">
                <span>Mon</span><span>Sun</span>
                </div>
            </GlassCard>

            {/* Today's Goal Tracker */}
            <GlassCard className="mt-4 mx-4 px-5 py-4">
                <div className="flex justify-between items-center mb-2">
                <span className="font-bold text-white/80">Today's Goal</span>
                <span className="text-xs text-electric font-semibold">
                  {isLoadingToday || isLoadingSettings ? '...' : `${todayDistance.toFixed(1)} / ${dailyGoal} km`}
                </span>
                </div>
                <ProgressBar percent={isLoadingToday || isLoadingSettings ? 0 : dailyProgress} />
                <div className="flex justify-between text-2xs text-white/60 mt-1">
                <span>0km</span>
                <span>{isLoadingSettings ? '...' : `${dailyGoal}km`}</span>
                </div>
            </GlassCard>

            {/* Recent Achievements Carousel */}
            <div className="mt-5 px-4">
                <h2 className="gradient-title text-lg mb-2">Recent Achievements</h2>
                <div className="flex gap-3 overflow-x-auto scroll-smooth pb-2 snap-x">
                {sampleAchievements.map((a) => (
                    <GlassCard
                    key={a.id}
                    className="min-w-[110px] snap-center items-center flex flex-col justify-center py-4 shadow-glow hover:scale-105 transition-transform"
                    >
                    <div className="w-10 h-10 mb-1 flex items-center justify-center">
                        {a.icon}
                    </div>
                    <span className="text-xs text-white text-center">{a.title}</span>
                    </GlassCard>
                ))}
                </div>
            </div>

            {/* Quick Stats */}
            <div className="mt-7 grid grid-cols-3 gap-4 px-4">
                {statCards.map((card) => (
                <GlassCard
                    key={card.label}
                    className={`stat-card bg-gradient-to-br ${card.color} !bg-clip-padding`}
                >
                    <div className="stat-value">{card.value}</div>
                    <div className="stat-label">{card.label}</div>
                </GlassCard>
                ))}
            </div>

            {/* Bottom Navigation */}
            <BottomNav />
        </div>
    );
}

function GuestContent() {
    return (
      <div className="flex-1 flex flex-col items-center justify-center text-center px-4">
        <h1 className="text-4xl font-bold gradient-title mb-4">Welcome to SoloGrind</h1>
        <p className="text-xl text-white/80 mb-8">
          Your personal journey to the top starts here.
        </p>
        <Link to="/auth">
          <Button size="lg" className="bg-electric hover:bg-purple text-white font-bold py-3 px-8 rounded-lg text-lg shadow-glow">
            Get Started
          </Button>
        </Link>
      </div>
    );
}

export default function Index() {
  const { user, loading } = useAuth();

  return (
    <>
      <div
        style={{ backgroundImage: "url('https://images.unsplash.com/photo-1470813740244-df37b8c1edcb')" }}
        className="fixed inset-0 bg-center bg-cover filter blur-sm scale-105"
      />
      <div className="relative z-10 pb-20 pt-6 min-h-screen bg-black/70 flex flex-col">
        {loading ? (
          <div className="flex-1 flex items-center justify-center">
            <Loader2 className="h-8 w-8 text-electric animate-spin" />
          </div>
        ) : user ? (
          <AuthenticatedContent />
        ) : (
          <GuestContent />
        )}
      </div>
    </>
  );
}
